#include "backpressurecontroller.h"
#include "log.h"
#include <algorithm>

BackpressureController::BackpressureController(QObject *parent)
    : QObject(parent)
{
    logInfo(QString("BackpressureController created with thresholds - Warning: %1, Throttle: %2, Block: %3")
           .arg(m_warningThreshold).arg(m_throttleThreshold).arg(m_blockThreshold));
}

BackpressureController::~BackpressureController()
{
    logInfo(QString("BackpressureController destroyed - Total checks: %1, Warnings: %2, Throttles: %3, Blocks: %4")
           .arg(m_totalChecks.load()).arg(m_warningCount.load())
           .arg(m_throttleCount.load()).arg(m_blockCount.load()));
}

void BackpressureController::setBackpressureCallback(std::function<void(const QString&, bool)> callback)
{
    m_backpressureCallback = callback;
    logInfo("Backpressure callback function set");
}

void BackpressureController::setWarningThreshold(double threshold)
{
    if (threshold < 0.0 || threshold > 1.0) {
        logWarnning(QString("Invalid warning threshold %1, must be between 0.0 and 1.0").arg(threshold));
        return;
    }
    
    m_warningThreshold = threshold;
    logInfo(QString("Warning threshold set to: %1").arg(threshold));
}

void BackpressureController::setThrottleThreshold(double threshold)
{
    if (threshold < 0.0 || threshold > 1.0) {
        logWarnning(QString("Invalid throttle threshold %1, must be between 0.0 and 1.0").arg(threshold));
        return;
    }
    
    m_throttleThreshold = threshold;
    logInfo(QString("Throttle threshold set to: %1").arg(threshold));
}

void BackpressureController::setBlockThreshold(double threshold)
{
    if (threshold < 0.0 || threshold > 1.0) {
        logWarnning(QString("Invalid block threshold %1, must be between 0.0 and 1.0").arg(threshold));
        return;
    }
    
    m_blockThreshold = threshold;
    logInfo(QString("Block threshold set to: %1").arg(threshold));
}

BackpressureController::BackpressureLevel BackpressureController::checkAndTriggerBackpressure(
    const QString& sensorId, size_t currentSize, size_t maxSize)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to checkAndTriggerBackpressure");
        return BackpressureLevel::None;
    }
    
    if (maxSize == 0) {
        logError("Zero max size provided to checkAndTriggerBackpressure");
        return BackpressureLevel::None;
    }
    
    m_totalChecks++;
    
    try {
        double utilizationRate = calculateUtilizationRate(currentSize, maxSize);
        BackpressureLevel newLevel = determineBackpressureLevel(utilizationRate);
        
        BackpressureLevel oldLevel = m_currentLevel.load();
        m_currentLevel.store(newLevel);
        
        // 更新统计信息
        updateStatistics(newLevel);
        
        // 如果级别发生变化，触发相应的处理
        if (newLevel != oldLevel) {
            logInfo(QString("Backpressure level changed for sensor %1: %2 -> %3, utilization: %4%")
                   .arg(sensorId).arg(backpressureLevelToString(oldLevel))
                   .arg(backpressureLevelToString(newLevel)).arg(utilizationRate * 100, 0, 'f', 2));
            
            emit backpressureLevelChanged(sensorId, static_cast<int>(newLevel), utilizationRate);
        }
        
        // 根据级别执行相应的动作
        switch (newLevel) {
            case BackpressureLevel::Warning:
                emit backpressureWarning(sensorId, QString("Cache utilization warning: %1%").arg(utilizationRate * 100, 0, 'f', 2));
                if (m_backpressureCallback) {
                    m_backpressureCallback(sensorId, false);
                }
                break;
                
            case BackpressureLevel::Throttle:
                logWarnning(QString("Throttling sensor %1 due to high cache utilization: %2%")
                           .arg(sensorId).arg(utilizationRate * 100, 0, 'f', 2));
                if (m_backpressureCallback) {
                    m_backpressureCallback(sensorId, false);
                }
                break;
                
            case BackpressureLevel::Block:
                logWarnning(QString("Blocking sensor %1 due to critical cache utilization: %2%")
                           .arg(sensorId).arg(utilizationRate * 100, 0, 'f', 2));
                if (m_backpressureCallback) {
                    m_backpressureCallback(sensorId, true);
                }
                break;
                
            case BackpressureLevel::None:
            default:
                // 正常状态，无需特殊处理
                break;
        }
        
        return newLevel;
        
    } catch (const std::exception& e) {
        logError(QString("Exception in checkAndTriggerBackpressure for sensor %1: %2").arg(sensorId).arg(e.what()));
        return BackpressureLevel::None;
    } catch (...) {
        logError(QString("Unknown exception in checkAndTriggerBackpressure for sensor: %1").arg(sensorId));
        return BackpressureLevel::None;
    }
}

QString BackpressureController::getBackpressureStatistics() const
{
    std::lock_guard<std::mutex> lock(m_statisticsMutex);

    size_t total = m_totalChecks.load();
    size_t warnings = m_warningCount.load();
    size_t throttles = m_throttleCount.load();
    size_t blocks = m_blockCount.load();

    double warningRate = (total > 0) ? (static_cast<double>(warnings) / total * 100.0) : 0.0;
    double throttleRate = (total > 0) ? (static_cast<double>(throttles) / total * 100.0) : 0.0;
    double blockRate = (total > 0) ? (static_cast<double>(blocks) / total * 100.0) : 0.0;

    return QString("Backpressure Statistics - Total Checks: %1, Warnings: %2 (%3%), "
                  "Throttles: %4 (%5%), Blocks: %6 (%7%), Current Level: %8")
           .arg(total).arg(warnings).arg(warningRate, 0, 'f', 2)
           .arg(throttles).arg(throttleRate, 0, 'f', 2)
           .arg(blocks).arg(blockRate, 0, 'f', 2)
           .arg(backpressureLevelToString(m_currentLevel.load()));
}

void BackpressureController::resetStatistics()
{
    std::lock_guard<std::mutex> lock(m_statisticsMutex);

    m_warningCount.store(0);
    m_throttleCount.store(0);
    m_blockCount.store(0);
    m_totalChecks.store(0);
    m_currentLevel.store(BackpressureLevel::None);

    logInfo("Backpressure statistics reset");
}

void BackpressureController::triggerBackpressure(const QString& sensorId, BackpressureLevel level)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to triggerBackpressure");
        return;
    }

    try {
        BackpressureLevel oldLevel = m_currentLevel.load();
        m_currentLevel.store(level);

        updateStatistics(level);

        logInfo(QString("Manual backpressure triggered for sensor %1: %2")
               .arg(sensorId).arg(backpressureLevelToString(level)));

        emit backpressureLevelChanged(sensorId, static_cast<int>(level), 0.0);

        if (m_backpressureCallback) {
            bool shouldBlock = (level == BackpressureLevel::Block);
            m_backpressureCallback(sensorId, shouldBlock);
        }

    } catch (const std::exception& e) {
        logError(QString("Exception in triggerBackpressure for sensor %1: %2").arg(sensorId).arg(e.what()));
    } catch (...) {
        logError(QString("Unknown exception in triggerBackpressure for sensor: %1").arg(sensorId));
    }
}

double BackpressureController::calculateUtilizationRate(size_t currentSize, size_t maxSize) const
{
    if (maxSize == 0) {
        return 0.0;
    }

    return std::min(1.0, static_cast<double>(currentSize) / static_cast<double>(maxSize));
}

BackpressureController::BackpressureLevel BackpressureController::determineBackpressureLevel(double utilizationRate) const
{
    if (utilizationRate >= m_blockThreshold) {
        return BackpressureLevel::Block;
    } else if (utilizationRate >= m_throttleThreshold) {
        return BackpressureLevel::Throttle;
    } else if (utilizationRate >= m_warningThreshold) {
        return BackpressureLevel::Warning;
    } else {
        return BackpressureLevel::None;
    }
}

QString BackpressureController::backpressureLevelToString(BackpressureLevel level) const
{
    switch (level) {
        case BackpressureLevel::None:
            return "None";
        case BackpressureLevel::Warning:
            return "Warning";
        case BackpressureLevel::Throttle:
            return "Throttle";
        case BackpressureLevel::Block:
            return "Block";
        default:
            return "Unknown";
    }
}

void BackpressureController::updateStatistics(BackpressureLevel level)
{
    try {
        switch (level) {
            case BackpressureLevel::Warning:
                m_warningCount++;
                break;
            case BackpressureLevel::Throttle:
                m_throttleCount++;
                break;
            case BackpressureLevel::Block:
                m_blockCount++;
                break;
            case BackpressureLevel::None:
            default:
                // 正常状态，无需更新特殊计数
                break;
        }
    } catch (const std::exception& e) {
        logError(QString("Exception in updateStatistics: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception in updateStatistics");
    }
}
