# AsyncDataProcessor 异步数据处理器模块
# 提供高性能的传感器数据异步处理功能

HEADERS += \
    $$PWD/sensordatacache.h \
    $$PWD/cachemanager.h \
    $$PWD/processscheduler.h \
    $$PWD/dataprocessor.h \
    $$PWD/backpressurecontroller.h \
    $$PWD/asyncdataprocessor.h

SOURCES += \
    $$PWD/sensordatacache.cpp \
    $$PWD/cachemanager.cpp \
    $$PWD/processscheduler.cpp \
    $$PWD/dataprocessor.cpp \
    $$PWD/backpressurecontroller.cpp \
    $$PWD/asyncdataprocessor.cpp

# 包含路径
INCLUDEPATH += $$PWD

# 依赖的Qt模块
QT += core concurrent

# C++标准
CONFIG += c++11

# 编译器特定设置
unix {
    # ARM平台优化
    if(contains(DEFINES, ARM)) {
        # ARM单核优化设置
        QMAKE_CXXFLAGS += -O2 -march=native
        DEFINES += ASYNC_PROCESSOR_ARM_OPTIMIZED
    }
}

# 调试信息
CONFIG(debug, debug|release) {
    DEFINES += ASYNC_PROCESSOR_DEBUG
}

# 发布版本优化
CONFIG(release, debug|release) {
    DEFINES += ASYNC_PROCESSOR_RELEASE
    QMAKE_CXXFLAGS += -O2
}
