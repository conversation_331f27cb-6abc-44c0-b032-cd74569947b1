#include "processscheduler.h"
#include "log.h"
#include <QDateTime>
#include <algorithm>

ProcessScheduler::ProcessScheduler()
{
    logInfo("ProcessScheduler created with default configuration");
    logInfo(QString("Default settings - BatchSize: %1, MaxBatchSize: %2, TimeThreshold: %3ms, IdleThreshold: %4ms")
           .arg(m_batchSize).arg(m_maxBatchSize).arg(m_timeThreshold).arg(m_idleThreshold));
}

ProcessScheduler::~ProcessScheduler()
{
    std::lock_guard<std::mutex> lock(m_processingMutex);
    if (!m_processingSensors.isEmpty()) {
        logWarnning(QString("ProcessScheduler destroyed with %1 sensors still in processing state")
                   .arg(m_processingSensors.size()));
    }
    
    logInfo(QString("ProcessScheduler destroyed - Total scheduled: %1, Total processed: %2")
           .arg(m_totalScheduled.load()).arg(m_totalProcessed.load()));
}

void ProcessScheduler::setBatchSize(size_t batchSize)
{
    if (batchSize == 0) {
        logWarnning("Attempt to set batch size to 0, using default value 25");
        batchSize = 25;
    }
    
    m_batchSize = batchSize;
    logInfo(QString("Batch size set to: %1").arg(batchSize));
}

void ProcessScheduler::setTimeThreshold(qint64 timeThreshold)
{
    if (timeThreshold < 0) {
        logWarnning("Negative time threshold provided, using absolute value");
        timeThreshold = std::abs(timeThreshold);
    }
    
    m_timeThreshold = timeThreshold;
    logInfo(QString("Time threshold set to: %1ms").arg(timeThreshold));
}

void ProcessScheduler::setIdleThreshold(qint64 idleThreshold)
{
    if (idleThreshold < 0) {
        logWarnning("Negative idle threshold provided, using absolute value");
        idleThreshold = std::abs(idleThreshold);
    }
    
    m_idleThreshold = idleThreshold;
    logInfo(QString("Idle threshold set to: %1ms").arg(idleThreshold));
}

void ProcessScheduler::setMaxBatchSize(size_t maxBatchSize)
{
    if (maxBatchSize == 0) {
        logWarnning("Attempt to set max batch size to 0, using default value 100");
        maxBatchSize = 100;
    }
    
    if (maxBatchSize < m_batchSize) {
        logWarnning(QString("Max batch size %1 is smaller than batch size %2, adjusting batch size")
                   .arg(maxBatchSize).arg(m_batchSize));
        m_batchSize = maxBatchSize;
    }
    
    m_maxBatchSize = maxBatchSize;
    logInfo(QString("Max batch size set to: %1").arg(maxBatchSize));
}

QString ProcessScheduler::getNextSensorToProcess(CacheManager* cacheManager)
{
    if (!cacheManager) {
        logError("Null cache manager provided to getNextSensorToProcess");
        return QString();
    }
    
    QStringList sensorIds = cacheManager->getAllSensorIds();
    if (sensorIds.isEmpty()) {
        logTrace("No sensors available for processing");
        return QString();
    }
    
    m_totalScheduled++;
    
    try {
        // 找到上次处理的传感器在当前列表中的位置
        int startIndex = findStartIndex(sensorIds);
        
        std::lock_guard<std::mutex> lock(m_processingMutex);
        
        // 从startIndex开始循环查找需要处理且未在处理中的传感器
        for (int i = 0; i < sensorIds.size(); ++i) {
            int currentIndex = (startIndex + i) % sensorIds.size();
            QString sensorId = sensorIds[currentIndex];
            
            // 跳过正在处理的传感器
            if (m_processingSensors.contains(sensorId)) {
                logTrace(QString("Sensor %1 is already being processed, skipping").arg(sensorId));
                continue;
            }
            
            SensorDataCache* cache = cacheManager->getSensorCache(sensorId);
            if (cache && shouldProcess(*cache)) {
                m_processingSensors.insert(sensorId);  // 标记为处理中
                m_lastProcessedSensorId = sensorId;    // 更新记录
                
                logTrace(QString("Selected sensor %1 for processing, cache size: %2, time since last add: %3ms")
                        .arg(sensorId).arg(cache->size()).arg(cache->timeSinceLastAdd()));
                
                return sensorId;
            }
        }
        
        logTrace("No sensor needs processing in current cycle");
        return QString(); // 没有传感器需要处理
        
    } catch (const std::exception& e) {
        logError(QString("Exception in getNextSensorToProcess: %1").arg(e.what()));
        return QString();
    } catch (...) {
        logError("Unknown exception in getNextSensorToProcess");
        return QString();
    }
}

void ProcessScheduler::markProcessingComplete(const QString& sensorId)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to markProcessingComplete");
        return;
    }

    try {
        std::lock_guard<std::mutex> lock(m_processingMutex);

        if (m_processingSensors.remove(sensorId)) {
            m_totalProcessed++;
            logTrace(QString("Marked sensor %1 as processing complete").arg(sensorId));
        } else {
            logWarnning(QString("Sensor %1 was not in processing state").arg(sensorId));
        }

    } catch (const std::exception& e) {
        logError(QString("Exception in markProcessingComplete for sensor %1: %2").arg(sensorId).arg(e.what()));
    } catch (...) {
        logError(QString("Unknown exception in markProcessingComplete for sensor: %1").arg(sensorId));
    }
}

size_t ProcessScheduler::calculateBatchSize(qint64 timeSinceLastGlobalUpdate) const
{
    try {
        if (timeSinceLastGlobalUpdate > m_idleThreshold) {
            logTrace(QString("System idle for %1ms, using max batch size: %2")
                    .arg(timeSinceLastGlobalUpdate).arg(m_maxBatchSize));
            return m_maxBatchSize;
        } else {
            logTrace(QString("System active, using normal batch size: %1").arg(m_batchSize));
            return m_batchSize;
        }
    } catch (const std::exception& e) {
        logError(QString("Exception in calculateBatchSize: %1").arg(e.what()));
        return m_batchSize; // 返回默认值
    } catch (...) {
        logError("Unknown exception in calculateBatchSize");
        return m_batchSize;
    }
}

bool ProcessScheduler::shouldProcess(const SensorDataCache& cache) const
{
    try {
        // 检查数据量阈值
        if (cache.size() >= m_batchSize) {
            logTrace(QString("Sensor %1 meets size threshold: %2 >= %3")
                    .arg(cache.sensorId()).arg(cache.size()).arg(m_batchSize));
            return true;
        }

        // 检查时间阈值
        if (cache.size() > 0 && cache.timeSinceLastAdd() > m_timeThreshold) {
            logTrace(QString("Sensor %1 meets time threshold: %2ms > %3ms, size: %4")
                    .arg(cache.sensorId()).arg(cache.timeSinceLastAdd()).arg(m_timeThreshold).arg(cache.size()));
            return true;
        }

        return false;

    } catch (const std::exception& e) {
        logError(QString("Exception in shouldProcess for sensor %1: %2").arg(cache.sensorId()).arg(e.what()));
        return false;
    } catch (...) {
        logError(QString("Unknown exception in shouldProcess for sensor: %1").arg(cache.sensorId()));
        return false;
    }
}

size_t ProcessScheduler::getProcessingCount() const
{
    std::lock_guard<std::mutex> lock(m_processingMutex);
    return m_processingSensors.size();
}

QString ProcessScheduler::getSchedulingStatistics() const
{
    std::lock_guard<std::mutex> lock(m_processingMutex);

    return QString("Scheduling Statistics - Total Scheduled: %1, Total Processed: %2, Currently Processing: %3, Last Processed: %4")
           .arg(m_totalScheduled.load())
           .arg(m_totalProcessed.load())
           .arg(m_processingSensors.size())
           .arg(m_lastProcessedSensorId.isEmpty() ? "None" : m_lastProcessedSensorId);
}

void ProcessScheduler::resetSchedulingState()
{
    std::lock_guard<std::mutex> lock(m_processingMutex);

    size_t clearedCount = m_processingSensors.size();
    m_processingSensors.clear();
    m_lastProcessedSensorId.clear();

    logInfo(QString("Scheduling state reset, cleared %1 processing sensors").arg(clearedCount));
}

int ProcessScheduler::findStartIndex(const QStringList& sensorIds) const
{
    if (m_lastProcessedSensorId.isEmpty() || sensorIds.isEmpty()) {
        return 0;
    }

    int lastIndex = sensorIds.indexOf(m_lastProcessedSensorId);
    if (lastIndex >= 0) {
        // 从上次处理的传感器的下一个开始
        return (lastIndex + 1) % sensorIds.size();
    }

    // 如果上次的传感器已经不在列表中，从头开始
    logTrace(QString("Last processed sensor %1 not found in current list, starting from beginning")
            .arg(m_lastProcessedSensorId));
    return 0;
}
