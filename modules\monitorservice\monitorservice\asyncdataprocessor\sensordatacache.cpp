#include "sensordatacache.h"
#include "log.h"
#include <QDateTime>

SensorDataCache::SensorDataCache(const QString& sensorId)
    : m_sensorId(sensorId)
{
    logTrace(QString("SensorDataCache created for sensor: %1").arg(sensorId));
}

SensorDataCache::~SensorDataCache()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    size_t remainingItems = m_dataQueue.size();
    if (remainingItems > 0) {
        logWarnning(QString("SensorDataCache destroyed with %1 remaining items for sensor: %2")
                   .arg(remainingItems).arg(m_sensorId));
    }
    logTrace(QString("SensorDataCache destroyed for sensor: %1").arg(m_sensorId));
}

void SensorDataCache::addData(const QList<QSharedPointer<DataItem>>& items)
{
    if (items.isEmpty()) {
        logWarnning(QString("Empty data list provided for sensor: %1").arg(m_sensorId));
        return;
    }

    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 添加数据到队列
        for (const auto& item : items) {
            if (item.isNull()) {
                logWarnning(QString("Null data item encountered for sensor: %1").arg(m_sensorId));
                continue;
            }
            m_dataQueue.push(item);
        }
        
        // 更新原子变量
        m_size.store(m_dataQueue.size());
        m_lastAddTime.store(QDateTime::currentMSecsSinceEpoch());
        
        logTrace(QString("Added %1 items to sensor cache: %2, total size: %3")
                .arg(items.size()).arg(m_sensorId).arg(m_size.load()));
                
    } catch (const std::exception& e) {
        logError(QString("Exception in addData for sensor %1: %2").arg(m_sensorId).arg(e.what()));
    } catch (...) {
        logError(QString("Unknown exception in addData for sensor: %1").arg(m_sensorId));
    }
}

QList<QSharedPointer<DataItem>> SensorDataCache::extractData(size_t maxCount)
{
    QList<QSharedPointer<DataItem>> result;
    
    if (maxCount == 0) {
        logTrace(QString("Zero maxCount provided for sensor: %1").arg(m_sensorId));
        return result;
    }

    try {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        size_t extractCount = std::min(maxCount, m_dataQueue.size());
        if (extractCount == 0) {
            logTrace(QString("No data to extract for sensor: %1").arg(m_sensorId));
            return result;
        }
        
        result.reserve(extractCount);
        
        // 从队列中提取数据
        for (size_t i = 0; i < extractCount; ++i) {
            if (!m_dataQueue.empty()) {
                auto item = m_dataQueue.front();
                m_dataQueue.pop();
                if (!item.isNull()) {
                    result.append(item);
                } else {
                    logWarnning(QString("Null item extracted from queue for sensor: %1").arg(m_sensorId));
                }
            }
        }
        
        // 更新缓存大小
        m_size.store(m_dataQueue.size());
        
        logTrace(QString("Extracted %1 items from sensor cache: %2, remaining: %3")
                .arg(result.size()).arg(m_sensorId).arg(m_size.load()));
                
    } catch (const std::exception& e) {
        logError(QString("Exception in extractData for sensor %1: %2").arg(m_sensorId).arg(e.what()));
        result.clear(); // 确保异常情况下返回空列表
    } catch (...) {
        logError(QString("Unknown exception in extractData for sensor: %1").arg(m_sensorId));
        result.clear();
    }
    
    return result;
}

qint64 SensorDataCache::timeSinceLastAdd() const
{
    qint64 lastTime = m_lastAddTime.load();
    if (lastTime == 0) {
        return 0; // 从未添加过数据
    }
    
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    return currentTime - lastTime;
}
