#include "dataprocessor.h"
#include "log.h"
#include "../uploaddatahandler.h"
#include "../synctask.h"

DataProcessor::DataProcessor(QObject *parent)
    : QObject(parent)
{
    logInfo("DataProcessor created");
}

DataProcessor::~DataProcessor()
{

}

bool DataProcessor::processData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& data)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to processData");     
        return false;
    }
    
    if (data.isEmpty()) {
        logWarnning(QString("Empty data list provided for sensor: %1").arg(sensorId));
        return true;
    }
    
    logInfo(QString("Starting data processing for sensor %1, item count: %2").arg(sensorId).arg(data.size()));
    
    // 数据去重
    logInfo(QString("DataProcessor::processData - Deduplicating data for sensor [%1]").arg(sensorId));
    QList<QSharedPointer<DataItem>> filteredDataItems = deduplicateData(sensorId, data);
    int duplicateCount = data.size() - filteredDataItems.size();
    logInfo(QString("DataProcessor::processData - Sensor [%1], deduplication: total [%2], new [%3], duplicates [%4]")
            .arg(sensorId).arg(data.size()).arg(filteredDataItems.size()).arg(duplicateCount));

    if (filteredDataItems.isEmpty()) {
        logInfo(QString("DataProcessor::processData - Sensor [%1], no new data to process")
                 .arg(sensorId));

        // 更新同步计数（使用原始数量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(sensorId, data.size());
            emit dataProcessed(sensorId, data.size(), true);
        }
        return true;
    }

    if(0 != duplicateCount)
    {
        // 更新同步计数（重复数据量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(sensorId, duplicateCount);
            logInfo(QString("DataProcessor::processData - Updated sync count for sensor [%1], duplicates count: %2")
                     .arg(sensorId).arg(duplicateCount));
            emit dataProcessed(sensorId, duplicateCount, true);
        }
    }

    // 批量解析和入库
    logInfo(QString("DataProcessor::processData - Processing %1 filtered items for sensor [%2]")
             .arg(filteredDataItems.size()).arg(sensorId));

    UploadDataHandler batchHandler;
    batchHandler.setSensorId(sensorId);
    batchHandler.addData(filteredDataItems);

    logInfo(QString("DataProcessor::processData - Parsing data for sensor [%1]")
             .arg(sensorId));
    batchHandler.parseData();
    logInfo(QString("DataProcessor::processData - Parsing data for sensor [%1] completed")
             .arg(sensorId));

    logInfo(QString("DataProcessor::processData - Saving data to DB for sensor [%1]")
             .arg(sensorId));
    batchHandler.saveDataToDB();
    logInfo(QString("DataProcessor::processData - Saving data to DB for sensor [%1] completed")
             .arg(sensorId));

    emit dataProcessed(sensorId, filteredDataItems.size(), true);

    return true;
}

QList<QSharedPointer<DataItem> > DataProcessor::deduplicateData(const QString &sensorId, const QList<QSharedPointer<DataItem> > &dataItems)
{
    if (m_syncTask.isNull()) {
        logWarnning("UploadDataBackgroundProcessor::deduplicateData - SyncTask is null, cannot deduplicate");
        return dataItems;
    }

    // 获取传感器已存在的数据ID信息
    const SyncTask::SensorSyncInfo sensorSyncInfo = m_syncTask->getSensorSyncInfo(sensorId);
    logInfo(QString("DataProcessor::deduplicateData - Sensor [%1] has %2 existing data IDs")
             .arg(sensorId).arg(sensorSyncInfo.existingDataIds.size()));

    QList<QSharedPointer<DataItem>> filteredDataItems;

    for (int i = 0; i < dataItems.size(); ++i) {
        const auto& dataItem = dataItems[i];
        if (!sensorSyncInfo.existingDataIds.contains(dataItem->getDataId())) {
            filteredDataItems.append(dataItem);
        }
        else {
            logInfo(QString("UploadDataBackgroundProcessor::deduplicateData - Sensor [%1], Data ID [%2] already exists, skipped")
                     .arg(sensorId, QString::number(dataItem->getDataId())));
        }
    }

    return filteredDataItems;
}
