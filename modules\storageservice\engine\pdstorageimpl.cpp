#include "pdstorageimpl.h"
#include <QElapsedTimer>
#include "log.h"
#include "engineutils/engineutils.hpp"
#include "dbconfig.h"



namespace storage
{
Status AeStorageImpl::initStorage() noexcept
{
    m_storageengine = QSharedPointer<PDDB>::create(g_stDataBases[DB_AE]);
    if (!m_storageengine->open())
    {
        PDS_SYS_ERR_LOG("open database failed!");
        return Status::Aborted("open database failed!");
    }
    return Status::OK();
}

Status AeStorageImpl::addRecord(const QString &pointId, QSharedPointer<common::PdBaseData> data) noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }
    PDS_SYS_INFO_LOG("addAeRecord");
    auto unwrapdata = qSharedPointerDynamicCast<common::PdAeData>(data);

    if (!unwrapdata)
    {
        PDS_SYS_ERR_LOG("cannot unwrap aedata, pointId: %s", pointId.toLatin1().data());
        return Status::Aborted(std::string("cannot unwrap aedata, pointId: ") + pointId.toStdString());
    }

    if (!m_storageengine->addRecord(convertToDataRecord(pointId, *unwrapdata)))
    {
        PDS_SYS_ERR_LOG("add ae record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("add ae record failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("addAeRecord finished, pointId: %s", pointId.toLatin1().data());
    return rtn;
}

Status AeStorageImpl::addRecords(const QString &pointId, QList<QSharedPointer<common::PdBaseData> > &datas)
{
    Status rtn = Status::OK();

    if(!(rtn = checkInitStatus()).ok())
    {
        logError("checkInitStatus:") << rtn.ToString().c_str();
        return rtn;
    }
    logInfo("add ae data start, data size: ") << datas.size();

    DataRecordList dataRecords;
    dataRecords.reserve(datas.size());
    std::transform(datas.begin(), datas.end(), std::back_inserter(dataRecords), [&](const QSharedPointer<common::PdBaseData>& baseDataPtr) {
        // 尝试转换
        QSharedPointer<common::PdAeData> dataPtr = qSharedPointerDynamicCast<common::PdAeData>(baseDataPtr);
        if (dataPtr) {
            return convertToDataRecord(pointId, *dataPtr);
        }
        else
        {
            logWarnning("cannot unwrap ae data, pointId: ") << pointId;
            return DataRecord();
        }
    });

    logInfo("add AE Data Record, Record Size: ") << dataRecords.size();

    if (!m_storageengine->addRecords(dataRecords))
    {
        logError("add ae records failed, pointId: ") << pointId;
        rtn = Status::Incomplete(std::string("add ae records failed, pointId: ") + pointId.toStdString());
    }


    logInfo("add ae data finish, pointID: ") << pointId;

    return rtn;
}

Status AeStorageImpl::getRecordInTimePeriod(const QString &pointId,
                                            const QDateTime &startTime,
                                            const QDateTime &endTime,
                                            QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordInTimePeriod");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, startTime, endTime, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, itrValue)); });

    if (datas->empty())
    {
        rtn = Status::NotFound("no such value");
    }

    PDS_SYS_INFO_LOG("ae getRecordInTimePeriod end");
    return rtn;
}

Status AeStorageImpl::getRecordAtTimeMoment(const QString &pointId,
                                            const QDateTime &moment,
                                            QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordAtTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, moment, moment, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("ae getRecordAtTimeMoment end");
    return rtn;
}

Status AeStorageImpl::getRecordBeforeTimeMoment(const QString &pointId,
                                                const QDateTime &moment,
                                                uint64_t limit,
                                                QVector<QSharedPointer<common::PdBaseData> > *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordBeforeTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME, moment, limit, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, itrValue)); });

    PDS_SYS_INFO_LOG("ae getRecordBeforeTimeMoment end");
    return rtn;
}

Status AeStorageImpl::getRecordByIndex(const QString &pointId,
                                       int64_t index,
                                       QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordByIndex");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByIndex(pointId, IK_ID, index, index, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at index: %d, pointId: %s", index, pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("ae getRecordByIndex end");
    return rtn;
}

Status AeStorageImpl::getRecordByIndex(const QString &pointId,
                                       int64_t startindex,
                                       int64_t stopindex,
                                       QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordByIndex");
    auto pred = [&pointId, this](const DataRecord& record)
    { return QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, record)); };
    rtn = getRecordByIndexWithPred(pointId, startindex, stopindex, pred, datas);
    PDS_SYS_INFO_LOG("ae getRecordByIndex end");
    return rtn;
}

Status AeStorageImpl::getRecordByGlobalIndex(int64_t index,
                                             QSharedPointer<common::PdBaseData>* data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getRecordByIndex");
    auto datarecord = m_storageengine->getRecord(index);

    *data = QSharedPointer<common::PdAeData>::create(convertFromDataRecord("", datarecord));

    PDS_SYS_INFO_LOG("ae getRecordByIndex end");
    return rtn;
}

Status AeStorageImpl::getTotalTableDataNum(const QString &pointId,
                                    int64_t &count) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae get Total Table Data Num");

    if (!m_storageengine->getTotalTableDataNum(pointId, count))
    {
        PDS_SYS_ERR_LOG("get Total Table Data Num failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get Total Table Data Num failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("ae get Total Table Data Num end");
    return rtn;
}

Status AeStorageImpl::getLastRecord(const QString &pointId,
                                    QSharedPointer<common::PdBaseData> *data) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("ae getLastRecord");
    DataRecord record;
    if (!m_storageengine->getLastRecord(pointId, record))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last record failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        PDS_SYS_TRACE_LOG("before convert:%d", elaspedTimer.elapsed());
        *data = QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, record));
        PDS_SYS_TRACE_LOG("after convert:%d", elaspedTimer.elapsed());
    }

    PDS_SYS_INFO_LOG("ae getLastRecord end");
    return rtn;
}

Status AeStorageImpl::getLastRecord(const QString &pointId,
                                    uint64_t limit,
                                    QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    QElapsedTimer elaspedTimer;
    elaspedTimer.start();
    PDS_SYS_INFO_LOG("ae getLastRecord");
    QSharedPointer<common::PdBaseData> lastdata;
    if (!(rtn = getLastRecord(pointId, &lastdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId,
                                                           IK_RECORDTIME,
                                                           lastdata->getRecordTime(),
                                                           limit,
                                                           &datarecords))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last records failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        PDS_SYS_INFO_LOG("before convert:%d", elaspedTimer.elapsed());
        std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                       [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
        { return QSharedPointer<common::PdAeData>::create(convertFromDataRecord(pointId, itrValue)); });
        PDS_SYS_INFO_LOG("after convert:%d", elaspedTimer.elapsed());

        if (datas->empty())
        {
            rtn = Status::NotFound("no such value");
        }
    }
    PDS_SYS_INFO_LOG("ae getLastRecord end");
    return rtn;
}

DataRecord AeStorageImpl::convertToDataRecord(const QString& pointId, const common::PdAeData &input) const
{
    DataRecord rtn = EngineUtils::convertFromBaseData(input);
    EngineUtils::convertFromPdEnvData(input.envConfig, &rtn);
    //rtn.setData(IK_GLOBAL_ID, m_storageengine->updateGlobalID(m_storageengine->getGlobalID(AE_GLOBAL_ID_START)));
    rtn.setData(IK_GLOBAL_ID, 0);
    rtn.setData(AE_MAX, input.aePeakValue);
    rtn.setData(AE_RMS, input.aeRms);
    rtn.setData(AE_FRE1_VALUE, input.aeFred1);
    rtn.setData(AE_FRE2_VALUE, input.aeFred2);

    rtn.setData(AE_SAMPLE_COUNT, input.aePhaseTotalCount);

    rtn.setData(AE_SAMPLE_RATE, input.aeSampleRate);
    rtn.setData(AE_TRIGGER_AMPLITUDE, input.triggerAmplitude);
    rtn.setData(AE_OPEN_TIME, input.openTime);
    rtn.setData(AE_CLOSE_TIME, input.closeTime);
    rtn.setData(AE_PULSE_INTERVAL_UNIT, input.pulseIntervalUnit);
    rtn.setData(AE_MAX_INTERVAL_TIME, input.maxIntervalTime);

    rtn.setData(AE_PHASE_ARRAY, EngineUtils::convertSequnceToByteArray(input.phaseArrayData));
    rtn.setData(AE_FLY_ARRAY, EngineUtils::convertSequnceToByteArray(input.flyArrayData));
    rtn.setData(AE_ARRAY, EngineUtils::convertSequnceToByteArray(input.aeData));

    if (!pointId.isEmpty())
    {
        rtn.setData(SAMPLE_DATA_POINT_GUID, pointId);
    }
    return rtn;
}

common::PdAeData AeStorageImpl::convertFromDataRecord(const QString& pointId, const DataRecord &input) const
{
    common::PdAeData rtn;
    EngineUtils::convertToBaseData(input, &rtn);
    EngineUtils::convertToPdEnvData(input, &rtn.envConfig);

    rtn.aePeakValue = input.value(AE_MAX).toFloat();
    rtn.aeRms = input.value(AE_RMS).toFloat();
    rtn.aeFred1 = input.value(AE_FRE1_VALUE).toFloat();
    rtn.aeFred2 = input.value(AE_FRE2_VALUE).toFloat();

    rtn.aePhaseTotalCount = input.value(AE_SAMPLE_COUNT).toInt();
    
    rtn.aeSampleRate = input.value(AE_SAMPLE_RATE).toInt();
    rtn.triggerAmplitude = input.value(AE_TRIGGER_AMPLITUDE).toFloat();
    rtn.openTime = input.value(AE_OPEN_TIME).toInt();
    rtn.closeTime = input.value(AE_CLOSE_TIME).toInt();
    rtn.pulseIntervalUnit = static_cast<common::PulseIntervalUnit>(input.value(AE_PULSE_INTERVAL_UNIT).toInt());
    rtn.maxIntervalTime = input.value(AE_MAX_INTERVAL_TIME).toInt();

    rtn.phaseArrayData = EngineUtils::convertByteArrayToSequnce<std::decay<decltype(rtn.phaseArrayData)>::type>(input.value(AE_PHASE_ARRAY).toByteArray());
    rtn.flyArrayData = EngineUtils::convertByteArrayToSequnce<std::decay<decltype(rtn.flyArrayData)>::type>(input.value(AE_FLY_ARRAY).toByteArray());
    rtn.aeData = EngineUtils::convertByteArrayToSequnce<std::decay<decltype(rtn.aeData)>::type>(input.value(AE_ARRAY).toByteArray());

    if (!pointId.isEmpty())
    {
        rtn.pointInfo.pointId = pointId;
    }
    return rtn;
}

/**********************************************************/

Status TevStorageImpl::initStorage() noexcept
{
    m_storageengine = QSharedPointer<PDDB>::create(g_stDataBases[DB_TEV]);
    if (!m_storageengine->open())
    {
        PDS_SYS_ERR_LOG("open database failed!");
        return Status::Aborted("open database failed!");
    }
    return Status::OK();
}

Status TevStorageImpl::addRecord(const QString &pointId, QSharedPointer<common::PdBaseData> data) noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }
    PDS_SYS_INFO_LOG("addTevRecord");
    auto unwrapdata = qSharedPointerDynamicCast<common::PdTevData>(data);

    if (!unwrapdata)
    {
        PDS_SYS_ERR_LOG("cannot unwrap tevdata, pointId: %s", pointId.toLatin1().data());
        return Status::Aborted(std::string("cannot unwrap tevdata, pointId: ") + pointId.toStdString());
    }

    if (!m_storageengine->addRecord(convertToDataRecord(pointId, *unwrapdata)))
    {
        PDS_SYS_ERR_LOG("add tev record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("add tev record failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("addTevRecord finished, pointId: %s", pointId.toLatin1().data());
    return rtn;
}

Status TevStorageImpl::addRecords(const QString &pointId, QList<QSharedPointer<common::PdBaseData> > &datas)
{
    Status rtn = Status::OK();
    if(!(rtn = checkInitStatus()).ok())
    {
        logError("checkInitStatus:") << rtn.ToString().c_str();
        return rtn;
    }
    logInfo("add tev data start, data size: ") << datas.size();
 
    DataRecordList dataRecords;
    dataRecords.reserve(datas.size());
    std::transform(datas.begin(), datas.end(), std::back_inserter(dataRecords), [&](const QSharedPointer<common::PdBaseData>& baseDataPtr) {
        // 尝试动态转换
        QSharedPointer<common::PdTevData> dataPtr = qSharedPointerDynamicCast<common::PdTevData>(baseDataPtr);
        if (dataPtr) {
            return convertToDataRecord(pointId, *dataPtr);
        }
        else
        {
            logWarnning("cannot unwrap tev data, pointId: ") << pointId;
            return DataRecord();
        }
    });

    logInfo("add TEV Data Record, Record Size: ") << dataRecords.size();

    if (!m_storageengine->addRecords(dataRecords))
    {
        logError("add tev records failed, pointId: ") << pointId;
        rtn = Status::Incomplete(std::string("add tev records failed, pointId: ") + pointId.toStdString());
    }

    logInfo("add tev data finish, pointID: ") << pointId;

    return rtn;
}

Status TevStorageImpl::getRecordInTimePeriod(const QString &pointId,
                                            const QDateTime &startTime,
                                            const QDateTime &endTime,
                                            QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordInTimePeriod");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, startTime, endTime, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, itrValue)); });

    if (rtn.ok() && datas->empty())
    {
        rtn = Status::NotFound("no such value");
    }

    PDS_SYS_INFO_LOG("tev getRecordInTimePeriod end");
    return rtn;
}

Status TevStorageImpl::getRecordAtTimeMoment(const QString &pointId,
                                            const QDateTime &moment,
                                            QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordAtTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, moment, moment, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("tev getRecordAtTimeMoment end");
    return rtn;
}

Status TevStorageImpl::getRecordBeforeTimeMoment(const QString &pointId,
                                                 const QDateTime &moment,
                                                 uint64_t limit,
                                                 QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordBeforeTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME, moment, limit, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, itrValue)); });

    PDS_SYS_INFO_LOG("tev getRecordBeforeTimeMoment end");
    return rtn;
}

Status TevStorageImpl::getRecordByIndex(const QString &pointId,
                                        int64_t index,
                                        QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordByIndex");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByIndex(pointId, IK_ID, index, index, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at index: %d, pointId: %s", index, pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("tev getRecordByIndex end");
    return rtn;
}

Status TevStorageImpl::getRecordByIndex(const QString &pointId,
                                        int64_t startindex,
                                        int64_t stopindex,
                                        QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordByIndex");
    auto pred = [&pointId, this](const DataRecord &record)
    { return QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, record)); };
    rtn = getRecordByIndexWithPred(pointId, startindex, stopindex, pred, datas);
    PDS_SYS_INFO_LOG("tev getRecordByIndex end");
    return rtn;
}

Status TevStorageImpl::getRecordByGlobalIndex(int64_t index,
                                              QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getRecordByIndex");
    auto datarecord = m_storageengine->getRecord(index);

    *data = QSharedPointer<common::PdTevData>::create(convertFromDataRecord("", datarecord));

    PDS_SYS_INFO_LOG("tev getRecordByIndex end");
    return rtn;
}

Status TevStorageImpl::getTotalTableDataNum(const QString &pointId,
                                    int64_t &count) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev get Total Table Data Num");

    if (!m_storageengine->getTotalTableDataNum(pointId, count))
    {
        PDS_SYS_ERR_LOG("get Total Table Data Num failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get Total Table Data Num failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("tev get Total Table Data Num end");
    return rtn;
}

Status TevStorageImpl::getLastRecord(const QString &pointId,
                                    QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_TRACE_LOG("tev getLastRecord");
    DataRecord record;
    if (!m_storageengine->getLastRecord(pointId, record))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last record failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        *data = QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, record));
    }

    PDS_SYS_TRACE_LOG("tev getLastRecord end");
    return rtn;
}

Status TevStorageImpl::getLastRecord(const QString &pointId,
                                    uint64_t limit,
                                    QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev getLastRecord");
    QSharedPointer<common::PdBaseData> lastdata;
    if (!(rtn = getLastRecord(pointId, &lastdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId,
                                                           IK_RECORDTIME,
                                                           lastdata->getRecordTime(),
                                                           limit,
                                                           &datarecords))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last records failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                       [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
        { return QSharedPointer<common::PdTevData>::create(convertFromDataRecord(pointId, itrValue)); });

        if (datas->empty())
        {
            rtn = Status::NotFound("no such value");
        }
    }

    PDS_SYS_INFO_LOG("tev getLastRecord end");
    return rtn;
}

DataRecord TevStorageImpl::convertToDataRecord(const QString& pointId, const common::PdTevData &input) const
{
    DataRecord rtn = EngineUtils::convertFromBaseData(input);
    EngineUtils::convertFromPdEnvData(input.envConfig, &rtn);
    //rtn.setData(IK_GLOBAL_ID, m_storageengine->updateGlobalID(m_storageengine->getGlobalID(TEV_GLOBAL_ID_START)));
    rtn.setData(IK_GLOBAL_ID, 0);
    rtn.setData(TEV_MAX, input.level);
    if (!pointId.isEmpty())
    {
        rtn.setData(SAMPLE_DATA_POINT_GUID, pointId);
    }
    return rtn;
}

common::PdTevData TevStorageImpl::convertFromDataRecord(const QString& pointId, const DataRecord &input) const
{
    common::PdTevData rtn;
    EngineUtils::convertToBaseData(input, &rtn);
    EngineUtils::convertToPdEnvData(input, &rtn.envConfig);
    rtn.level = input.value(TEV_MAX).toInt();
    rtn.pointInfo.pointId = pointId;
    return rtn;
}

/***************************************************************************/

Status UhfStorageImpl::initStorage() noexcept
{
    m_storageengine = QSharedPointer<PDDB>::create(g_stDataBases[DB_UHFPRPS]);
    if (!m_storageengine->open())
    {
        PDS_SYS_ERR_LOG("open database failed!");
        return Status::Aborted("open database failed!");
    }
    return Status::OK();
}

Status UhfStorageImpl::addRecord(const QString &pointId,
                                 QSharedPointer<common::PdBaseData> data) noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }
    PDS_SYS_INFO_LOG("addUhfRecord");
    auto unwrapdata = qSharedPointerDynamicCast<common::PdUhfData>(data);

    if (!unwrapdata)
    {
        PDS_SYS_ERR_LOG("cannot unwrap uhfdata, pointId: %s", pointId.toLatin1().data());
        return Status::Aborted(std::string("cannot unwrap uhfdata, pointId: ") + pointId.toStdString());
    }

    if (!m_storageengine->addRecord(convertToDataRecord(pointId, *unwrapdata)))
    {
        PDS_SYS_ERR_LOG("add uhf record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("add uhf record failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("addUhfRecord finished, pointId: %s", pointId.toLatin1().data());
    return rtn;
}

Status UhfStorageImpl::addRecords(const QString &pointId, QList<QSharedPointer<common::PdBaseData> > &datas)
{
    Status rtn = Status::OK();

    if(!(rtn = checkInitStatus()).ok())
    {
        logError("checkInitStatus:") << rtn.ToString().c_str();
        return rtn;
    }
    logInfo("add UHF data start, data size: ") << datas.size();

    DataRecordList dataRecords;
    dataRecords.reserve(datas.size());
    std::transform(datas.begin(), datas.end(), std::back_inserter(dataRecords), [&](const QSharedPointer<common::PdBaseData>& baseDataPtr) {
        // 尝试动态转换
        QSharedPointer<common::PdUhfData> uhfDataPtr = qSharedPointerDynamicCast<common::PdUhfData>(baseDataPtr);
        if (uhfDataPtr) {
            return convertToDataRecord(pointId, *uhfDataPtr);
        }
        else
        {
            logWarnning("cannot unwrap uhfdata, pointId: ") << pointId;
            return DataRecord();
        }
    });

    logInfo("add UHF Data Record, Record Size: ") << dataRecords.size();

    if (!m_storageengine->addRecords(dataRecords))
    {
        logError("add uhf records failed, pointId: ") << pointId;
        rtn = Status::Incomplete(std::string("add uhf records failed, pointId: ") + pointId.toStdString());
    }


    logInfo("add UHF data finish, pointID: ") << pointId;

    return rtn;
}

Status UhfStorageImpl::getRecordInTimePeriod(const QString &pointId,
                                            const QDateTime &startTime,
                                            const QDateTime &endTime,
                                            QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordInTimePeriod");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, startTime, endTime, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, itrValue)); });

    if (rtn.ok() && datas->empty())
    {
        rtn = Status::NotFound("no such value");
    }

    PDS_SYS_INFO_LOG("uhf getRecordInTimePeriod end");
    return rtn;
}

Status UhfStorageImpl::getRecordAtTimeMoment(const QString &pointId,
                                            const QDateTime &moment,
                                            QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordAtTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, moment, moment, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("uhf getRecordAtTimeMoment end");
    return rtn;
}

Status UhfStorageImpl::getRecordBeforeTimeMoment(const QString &pointId,
                                                 const QDateTime &moment,
                                                 uint64_t limit,
                                                 QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordBeforeTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME, moment, limit, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment: %s, pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, itrValue)); });

    PDS_SYS_INFO_LOG("uhf getRecordBeforeTimeMoment end");

    return rtn;
}

Status UhfStorageImpl::getRecordByIndex(const QString &pointId,
                                        int64_t index,
                                        QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordByIndex");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByIndex(pointId, IK_ID, index, index, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at index: %d, pointId: %s", index, pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("uhf getRecordByIndex end");
    return rtn;
}

Status UhfStorageImpl::getRecordByIndex(const QString &pointId,
                                        int64_t startindex,
                                        int64_t stopindex,
                                        QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordByIndex");
    auto pred = [&pointId, this](const DataRecord &record)
    { return QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, record)); };
    rtn = getRecordByIndexWithPred(pointId, startindex, stopindex, pred, datas);
    PDS_SYS_INFO_LOG("uhf getRecordByIndex end");
    return rtn;
}

Status UhfStorageImpl::getRecordByGlobalIndex(int64_t index,
                                              QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getRecordByIndex");
    auto datarecord = m_storageengine->getRecord(index);

    *data = QSharedPointer<common::PdUhfData>::create(convertFromDataRecord("", datarecord));

    PDS_SYS_INFO_LOG("uhf getRecordByIndex end");
    return rtn;
}

Status UhfStorageImpl::getTotalTableDataNum(const QString &pointId,
                                    int64_t &count) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf get Total Table Data Num");

    if (!m_storageengine->getTotalTableDataNum(pointId, count))
    {
        PDS_SYS_ERR_LOG("get Total Table Data Num failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get Total Table Data Num failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("uhf get Total Table Data Num end");
    return rtn;
}

Status UhfStorageImpl::getLastRecord(const QString &pointId,
                                    QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getLastRecord");
    DataRecord record;
    if (!m_storageengine->getLastRecord(pointId, record))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::NotFound(std::string("get last record failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        *data = QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, record));
    }

    PDS_SYS_INFO_LOG("uhf getLastRecord end");
    return rtn;
}

Status UhfStorageImpl::getLastRecord(const QString &pointId,
                                    uint64_t limit,
                                    QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("uhf getLastRecord");
    QSharedPointer<common::PdBaseData> lastdata;
    if (!(rtn = getLastRecord(pointId, &lastdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId,
                                                           IK_RECORDTIME,
                                                           lastdata->getRecordTime(),
                                                           limit,
                                                           &datarecords))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last records failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                       [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
        { return QSharedPointer<common::PdUhfData>::create(convertFromDataRecord(pointId, itrValue)); });
        if (datas->empty())
        {
            rtn = Status::NotFound("no such value");
        }
    }

    PDS_SYS_INFO_LOG("uhf getLastRecord end");
    return rtn;
}

DataRecord UhfStorageImpl::convertToDataRecord(const QString &pointId, const common::PdUhfData &input) const
{
    auto rtn = EngineUtils::convertFromBaseData(input);
    EngineUtils::convertFromPdEnvData(input.envConfig, &rtn);
    EngineUtils::convertFromPdPrpsData(input.prpsData, &rtn);
    //rtn.setData(IK_GLOBAL_ID, m_storageengine->updateGlobalID(m_storageengine->getGlobalID(UHF_PRPS_GLOBAL_ID_START)));
    rtn.setData(IK_GLOBAL_ID, 0);
    if (!pointId.isEmpty())
    {
        rtn.setData(SAMPLE_DATA_POINT_GUID, pointId);
    }
    return rtn;
}

common::PdUhfData UhfStorageImpl::convertFromDataRecord(const QString &pointId, const DataRecord &input) const
{
    common::PdUhfData rtn;
    EngineUtils::convertToBaseData(input, &rtn);
    EngineUtils::convertToPdEnvData(input, &rtn.envConfig);
    EngineUtils::convertToPdPrpsData(input, &rtn.prpsData);
    rtn.pointInfo.pointId = pointId;
    return rtn;
}

/*****************************************************************************/

Status HfctStorageImpl::initStorage() noexcept
{
    m_storageengine = QSharedPointer<PDDB>::create(g_stDataBases[DB_HFCTPRPS]);
    if (!m_storageengine->open())
    {
        PDS_SYS_INFO_LOG("open database failed!");
        return Status::Aborted("open database failed!");
    }
    return Status::OK();
}

Status HfctStorageImpl::addRecord(const QString &pointId, QSharedPointer<common::PdBaseData> data) noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }
    PDS_SYS_INFO_LOG("addHfctRecord");
    auto unwrapdata = qSharedPointerDynamicCast<common::PdHfctData>(data);

    if (!unwrapdata)
    {
        PDS_SYS_ERR_LOG("cannot unwrap hfctdata, pointId: %s", pointId.toLatin1().data());
        return Status::Aborted(std::string("cannot unwrap hfctdata, pointId: ") + pointId.toStdString());
    }

    if (!m_storageengine->addRecord(convertToDataRecord(pointId, *unwrapdata)))
    {
        PDS_SYS_ERR_LOG("add hfct record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("add uhf record failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("addHfctRecord finished, pointId: %s", pointId.toLatin1().data());
    return rtn;
}

Status HfctStorageImpl::addRecords(const QString &pointId, QList<QSharedPointer<common::PdBaseData> > &datas)
{
    Status rtn = Status::OK();
    if(!(rtn = checkInitStatus()).ok())
    {
        logError("checkInitStatus:") << rtn.ToString().c_str();
        return rtn;
    }
    logInfo("add hfct data start, data size: ") << datas.size();

    DataRecordList dataRecords;
    dataRecords.reserve(datas.size());
    std::transform(datas.begin(), datas.end(), std::back_inserter(dataRecords), [&](const QSharedPointer<common::PdBaseData>& baseDataPtr) {
        // 尝试动态转换
        QSharedPointer<common::PdHfctData> dataPtr = qSharedPointerDynamicCast<common::PdHfctData>(baseDataPtr);
        if (dataPtr) {
            return convertToDataRecord(pointId, *dataPtr);
        }
        else
        {
            logWarnning("cannot unwrap hfct data, pointId: ") << pointId;
            return DataRecord();
        }
    });

    logInfo("add HFCT Data Record, Record Size: ") << dataRecords.size();

    if (!m_storageengine->addRecords(dataRecords))
    {
        logError("add hfct records failed, pointId: ") << pointId;
        rtn = Status::Incomplete(std::string("add hfct records failed, pointId: ") + pointId.toStdString());
    }

    logInfo("add hfct data finish, pointID: ") << pointId;

    return rtn;
}

Status HfctStorageImpl::getRecordInTimePeriod(const QString &pointId,
                                            const QDateTime &startTime,
                                            const QDateTime &endTime,
                                            QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordInTimePeriod");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, startTime, endTime, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, itrValue)); });

    if (datas->empty())
    {
        rtn = Status::NotFound("no such value");
    }

    PDS_SYS_INFO_LOG("hfct getRecordInTimePeriod end");
    return rtn;
}

Status HfctStorageImpl::getRecordAtTimeMoment(const QString &pointId,
                                            const QDateTime &moment,
                                            QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordAtTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, moment, moment, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment:%s , pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("hfct getRecordAtTimeMoment end");
    return rtn;
}

Status HfctStorageImpl::getRecordBeforeTimeMoment(const QString &pointId,
                                                  const QDateTime &moment,
                                                  uint64_t limit,
                                                  QVector<QSharedPointer<common::PdBaseData> > *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordBeforeTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME, moment, limit, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment:%s , pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, itrValue)); });

    PDS_SYS_INFO_LOG("hfct getRecordBeforeTimeMoment end");
    return rtn;
}

Status HfctStorageImpl::getRecordByIndex(const QString &pointId,
                                         int64_t index,
                                         QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordByIndex");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByIndex(pointId, IK_ID, index, index, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at index:%d , pointId: %s", index, pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("hfct getRecordByIndex end");
    return rtn;
}

Status HfctStorageImpl::getRecordByIndex(const QString &pointId,
                                         int64_t startindex,
                                         int64_t stopindex,
                                         QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordByIndex");
    auto pred = [&pointId, this](const DataRecord &record)
    { return QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, record)); };
    rtn = getRecordByIndexWithPred(pointId, startindex, stopindex, pred, datas);
    PDS_SYS_INFO_LOG("hfct getRecordByIndex end");
    return rtn;
}

Status HfctStorageImpl::getRecordByGlobalIndex(int64_t index,
                                               QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getRecordByIndex");
    auto datarecord = m_storageengine->getRecord(index);

    *data = QSharedPointer<common::PdHfctData>::create(convertFromDataRecord("", datarecord));

    PDS_SYS_INFO_LOG("hfct getRecordByIndex end");
    return rtn;
}

Status HfctStorageImpl::getTotalTableDataNum(const QString &pointId,
                                    int64_t &count) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct get Total Table Data Num");

    if (!m_storageengine->getTotalTableDataNum(pointId, count))
    {
        PDS_SYS_ERR_LOG("get Total Table Data Num failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get Total Table Data Num failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("hfct get Total Table Data Num end");
    return rtn;
}

Status HfctStorageImpl::getLastRecord(const QString &pointId,
                                    QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getLastRecord");
    DataRecord record;
    if (!m_storageengine->getLastRecord(pointId, record))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last record failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        *data = QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, record));
    }

    PDS_SYS_INFO_LOG("hfct getLastRecord end");
    return rtn;
}

Status HfctStorageImpl::getLastRecord(const QString &pointId,
                                    uint64_t limit,
                                    QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("hfct getLastRecord");
    QSharedPointer<common::PdBaseData> lastdata;
    if (!(rtn = getLastRecord(pointId, &lastdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId,
                                                           IK_RECORDTIME,
                                                           lastdata->getRecordTime(),
                                                           limit,
                                                           &datarecords))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last records failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                       [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
        { return QSharedPointer<common::PdHfctData>::create(convertFromDataRecord(pointId, itrValue)); });
        if (datas->empty())
        {
            rtn = Status::NotFound("no such value");
        }
    }
    PDS_SYS_INFO_LOG("hfct getLastRecord end");
    return rtn;
}

DataRecord HfctStorageImpl::convertToDataRecord(const QString &pointId, const common::PdHfctData &input) const
{
    auto rtn = EngineUtils::convertFromBaseData(input);
    EngineUtils::convertFromPdEnvData(input.envConfig, &rtn);
    EngineUtils::convertFromPdPrpsData(input.prpsData, &rtn);
    //rtn.setData(IK_GLOBAL_ID, m_storageengine->updateGlobalID(m_storageengine->getGlobalID(HFCT_PRPS_GLOBAL_ID_START)));
    rtn.setData(IK_GLOBAL_ID, 0);
    if (!pointId.isEmpty())
    {
        rtn.setData(SAMPLE_DATA_POINT_GUID, pointId);
    }
    return rtn;
}

common::PdHfctData HfctStorageImpl::convertFromDataRecord(const QString &pointId, const DataRecord &input) const
{
    common::PdHfctData rtn;
    EngineUtils::convertToBaseData(input, &rtn);
    EngineUtils::convertToPdEnvData(input, &rtn.envConfig);
    EngineUtils::convertToPdPrpsData(input, &rtn.prpsData);
    rtn.pointInfo.pointId = pointId;
    return rtn;
}


/*****************************************************************************/

Status TEVPRPSStorageImpl::initStorage() noexcept
{
    m_storageengine = QSharedPointer<PDDB>::create(g_stDataBases[DB_TEVPRPS]);
    if (!m_storageengine->open())
    {
        PDS_SYS_INFO_LOG("open database failed!");
        return Status::Aborted("open database failed!");
    }
    return Status::OK();
}

Status TEVPRPSStorageImpl::addRecord(const QString &pointId, QSharedPointer<common::PdBaseData> data) noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }
    PDS_SYS_INFO_LOG("addTEVPRPSRecord");
    auto unwrapdata = qSharedPointerDynamicCast<common::PdTEVPRPSData>(data);

    if (!unwrapdata)
    {
        PDS_SYS_ERR_LOG("cannot unwrap TEVPRPSdata, pointId: %s", pointId.toLatin1().data());
        return Status::Aborted(std::string("cannot unwrap TEVPRPSdata, pointId: ") + pointId.toStdString());
    }

    if (!m_storageengine->addRecord(convertToDataRecord(pointId, *unwrapdata)))
    {
        PDS_SYS_ERR_LOG("add TEVPRPS record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("add TEVPRPS record failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("addTEVPRPSRecord finished, pointId: %s", pointId.toLatin1().data());
    return rtn;
}

Status TEVPRPSStorageImpl::addRecords(const QString &pointId, QList<QSharedPointer<common::PdBaseData> > &datas)
{
    Status rtn = Status::OK();
    if(!(rtn = checkInitStatus()).ok())
    {
        logError("checkInitStatus:") << rtn.ToString().c_str();
        return rtn;
    }
    logInfo("add tevprps data start, data size: ") << datas.size();

    DataRecordList dataRecords;
    dataRecords.reserve(datas.size());
    std::transform(datas.begin(), datas.end(), std::back_inserter(dataRecords), [&](const QSharedPointer<common::PdBaseData>& baseDataPtr) {
        // 尝试动态转换
        QSharedPointer<common::PdTEVPRPSData> dataPtr = qSharedPointerDynamicCast<common::PdTEVPRPSData>(baseDataPtr);
        if (dataPtr) {  
            return convertToDataRecord(pointId, *dataPtr);
        }
        else
        {
            logWarnning("cannot unwrap tevprps data, pointId: ") << pointId;
            return DataRecord();
        }
    });

    logInfo("add TEVPRPS Data Record, Record Size: ") << dataRecords.size();

    if (!m_storageengine->addRecords(dataRecords))
    {
        logError("add tevprps records failed, pointId: ") << pointId;
        rtn = Status::Incomplete(std::string("add TEVPRPS records failed, pointId: ") + pointId.toStdString());
    }

    logInfo("add tevprps data finish, pointID: ") << pointId;

    return rtn;
}

Status TEVPRPSStorageImpl::getRecordInTimePeriod(const QString &pointId,
                                            const QDateTime &startTime,
                                            const QDateTime &endTime,
                                            QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordInTimePeriod");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, startTime, endTime, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, itrValue)); });

    if (datas->empty())
    {
        rtn = Status::NotFound("no such value");
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordInTimePeriod end");
    return rtn;
}

Status TEVPRPSStorageImpl::getRecordAtTimeMoment(const QString &pointId,
                                            const QDateTime &moment,
                                            QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordAtTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByTimeScan(pointId, IK_RECORDTIME, moment, moment, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment:%s , pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordAtTimeMoment end");
    return rtn;
}

Status TEVPRPSStorageImpl::getRecordBeforeTimeMoment(const QString &pointId,
                                                  const QDateTime &moment,
                                                  uint64_t limit,
                                                  QVector<QSharedPointer<common::PdBaseData> > *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordBeforeTimeMoment");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME, moment, limit, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at moment:%s , pointId: %s", moment.toString(DATETIME_FORMAT).toLatin1().data(), pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                   [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
    { return QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, itrValue)); });

    PDS_SYS_INFO_LOG("TEVPRPS getRecordBeforeTimeMoment end");
    return rtn;
}

Status TEVPRPSStorageImpl::getRecordByIndex(const QString &pointId,
                                         int64_t index,
                                         QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex");
    DataRecordList datarecords;
    if (!m_storageengine->getRecordByIndex(pointId, IK_ID, index, index, &datarecords))
    {
        PDS_SYS_ERR_LOG("scan record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("scan record failed, pointId: ") + pointId.toStdString());
    }

    if (datarecords.empty())
    {
        PDS_SYS_INFO_LOG("no data at index:%d , pointId: %s", index, pointId.toLatin1().data());
        rtn = Status::NotFound("no such value");
    }
    else
    {
        *data = QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, datarecords.front()));
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex end");
    return rtn;
}

Status TEVPRPSStorageImpl::getRecordByIndex(const QString &pointId,
                                         int64_t startindex,
                                         int64_t stopindex,
                                         QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex");
    auto pred = [&pointId, this](const DataRecord &record)
    { return QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, record)); };
    rtn = getRecordByIndexWithPred(pointId, startindex, stopindex, pred, datas);
    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex end");
    return rtn;
}

Status TEVPRPSStorageImpl::getRecordByGlobalIndex(int64_t index,
                                               QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex");
    auto datarecord = m_storageengine->getRecord(index);

    *data = QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord("", datarecord));

    PDS_SYS_INFO_LOG("TEVPRPS getRecordByIndex end");
    return rtn;
}

Status TEVPRPSStorageImpl::getTotalTableDataNum(const QString &pointId,
                                    int64_t &count) const noexcept
{
    QElapsedTimer elaspedTimer;
    elaspedTimer.start();

    Status rtn = Status::OK();
    if (!(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("tev get Total Table Data Num");

    if (!m_storageengine->getTotalTableDataNum(pointId, count))
    {
        PDS_SYS_ERR_LOG("get Total Table Data Num failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get Total Table Data Num failed, pointId: ") + pointId.toStdString());
    }

    PDS_SYS_INFO_LOG("tev get Total Table Data Num end");
    return rtn;
}

Status TEVPRPSStorageImpl::getLastRecord(const QString &pointId,
                                    QSharedPointer<common::PdBaseData> *data) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(data)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getLastRecord");
    DataRecord record;
    if (!m_storageengine->getLastRecord(pointId, record))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last record failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        *data = QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, record));
    }

    PDS_SYS_INFO_LOG("TEVPRPS getLastRecord end");
    return rtn;
}

Status TEVPRPSStorageImpl::getLastRecord(const QString &pointId,
                                    uint64_t limit,
                                    QVector<QSharedPointer<common::PdBaseData>> *datas) const noexcept
{
    Status rtn = Status::OK();
    if (!(rtn = checkPdData(datas)).ok() || !(rtn = checkInitStatus()).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    PDS_SYS_INFO_LOG("TEVPRPS getLastRecord");
    QSharedPointer<common::PdBaseData> lastdata;
    if (!(rtn = getLastRecord(pointId, &lastdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", rtn.ToString().c_str());
        return rtn;
    }

    DataRecordList datarecords;
    if (!m_storageengine->getRecordBeforeMomentByKeyColumn(pointId,
                                                           IK_RECORDTIME,
                                                           lastdata->getRecordTime(),
                                                           limit,
                                                           &datarecords))
    {
        PDS_SYS_ERR_LOG("get last record failed, pointId: %s", pointId.toLatin1().data());
        rtn = Status::Incomplete(std::string("get last records failed, pointId: ") + pointId.toStdString());
    }
    else
    {
        std::transform(datarecords.cbegin(), datarecords.cend(), std::back_inserter(*datas),
                       [&pointId, this](const std::decay<decltype(datarecords)>::type::value_type &itrValue)
        { return QSharedPointer<common::PdTEVPRPSData>::create(convertFromDataRecord(pointId, itrValue)); });
        if (datas->empty())
        {
            rtn = Status::NotFound("no such value");
        }
    }
    PDS_SYS_INFO_LOG("hfct getLastRecord end");
    return rtn;
}

DataRecord TEVPRPSStorageImpl::convertToDataRecord(const QString &pointId, const common::PdTEVPRPSData &input) const
{
    auto rtn = EngineUtils::convertFromBaseData(input);
    EngineUtils::convertFromPdEnvData(input.envConfig, &rtn);
    EngineUtils::convertFromPdPrpsData(input.prpsData, &rtn);
    //rtn.setData(IK_GLOBAL_ID, m_storageengine->updateGlobalID(m_storageengine->getGlobalID(TEVPRPS_PRPS_GLOBAL_ID_START)));
    rtn.setData(IK_GLOBAL_ID, 0);
    if (!pointId.isEmpty())
    {
        rtn.setData(SAMPLE_DATA_POINT_GUID, pointId);
    }
    return rtn;
}

common::PdTEVPRPSData TEVPRPSStorageImpl::convertFromDataRecord(const QString &pointId, const DataRecord &input) const
{
    common::PdTEVPRPSData rtn;
    EngineUtils::convertToBaseData(input, &rtn);
    EngineUtils::convertToPdEnvData(input, &rtn.envConfig);
    EngineUtils::convertToPdPrpsData(input, &rtn.prpsData);
    rtn.pointInfo.pointId = pointId;
    return rtn;
}

} // namespace storage


