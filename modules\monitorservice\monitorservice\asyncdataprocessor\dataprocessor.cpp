#include "dataprocessor.h"
#include "log.h"
#include "../fastsyncdata/uploaddatahandler.h"
#include <QElapsedTimer>
#include <QDateTime>
#include <algorithm>

DataProcessor::DataProcessor(QObject *parent)
    : QObject(parent)
{
    logInfo("DataProcessor created");
}

DataProcessor::~DataProcessor()
{
    logInfo(QString("DataProcessor destroyed - Total processed: %1, Successful: %2, Failed: %3")
           .arg(m_totalProcessed.load()).arg(m_totalSuccessful.load()).arg(m_totalFailed.load()));
}

bool DataProcessor::processData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& data)
{
    if (sensorId.isEmpty()) {
        logError("Empty sensor ID provided to processData");
        emit processingError(sensorId, "Empty sensor ID");
        return false;
    }
    
    if (data.isEmpty()) {
        logTrace(QString("Empty data list provided for sensor: %1").arg(sensorId));
        emit dataProcessed(sensorId, 0, true);
        return true;
    }
    
    QElapsedTimer timer;
    timer.start();
    
    logTrace(QString("Starting data processing for sensor %1, item count: %2").arg(sensorId).arg(data.size()));
    
    try {
        // 验证数据项
        QList<QSharedPointer<DataItem>> validData;
        validData.reserve(data.size());
        
        for (const auto& item : data) {
            if (validateDataItem(item)) {
                validData.append(item);
            } else {
                logWarnning(QString("Invalid data item found for sensor %1").arg(sensorId));
            }
        }
        
        if (validData.isEmpty()) {
            logWarnning(QString("No valid data items found for sensor: %1").arg(sensorId));
            qint64 processingTime = timer.elapsed();
            updateStatistics(0, false, processingTime);
            emit processingError(sensorId, "No valid data items");
            return false;
        }
        
        // 解析数据
        bool parseSuccess = parseData(validData);
        if (!parseSuccess) {
            logError(QString("Data parsing failed for sensor: %1").arg(sensorId));
            qint64 processingTime = timer.elapsed();
            updateStatistics(validData.size(), false, processingTime);
            emit processingError(sensorId, "Data parsing failed");
            return false;
        }
        
        // 保存到数据库
        bool saveSuccess = saveToDatabase(sensorId, validData);
        if (!saveSuccess) {
            logError(QString("Database save failed for sensor: %1").arg(sensorId));
            qint64 processingTime = timer.elapsed();
            updateStatistics(validData.size(), false, processingTime);
            emit processingError(sensorId, "Database save failed");
            return false;
        }
        
        qint64 processingTime = timer.elapsed();
        updateStatistics(validData.size(), true, processingTime);
        
        logTrace(QString("Successfully processed %1 items for sensor %2 in %3ms")
                .arg(validData.size()).arg(sensorId).arg(processingTime));
        
        emit dataProcessed(sensorId, validData.size(), true);
        return true;
        
    } catch (const std::exception& e) {
        qint64 processingTime = timer.elapsed();
        updateStatistics(data.size(), false, processingTime);
        
        QString errorMsg = QString("Exception in processData for sensor %1: %2").arg(sensorId).arg(e.what());
        logError(errorMsg);
        emit processingError(sensorId, errorMsg);
        return false;
        
    } catch (...) {
        qint64 processingTime = timer.elapsed();
        updateStatistics(data.size(), false, processingTime);
        
        QString errorMsg = QString("Unknown exception in processData for sensor: %1").arg(sensorId);
        logError(errorMsg);
        emit processingError(sensorId, errorMsg);
        return false;
    }
}

bool DataProcessor::parseData(const QList<QSharedPointer<DataItem>>& data)
{
    try {
        logTrace(QString("Parsing %1 data items").arg(data.size()));

        // 这里应该调用实际的数据解析逻辑
        // 目前使用简单的验证作为示例
        for (const auto& item : data) {
            if (!item || item->getContent().isEmpty()) {
                logWarnning("Found empty data item during parsing");
                return false;
            }
        }

        logTrace("Data parsing completed successfully");
        return true;

    } catch (const std::exception& e) {
        logError(QString("Exception in parseData: %1").arg(e.what()));
        return false;
    } catch (...) {
        logError("Unknown exception in parseData");
        return false;
    }
}

bool DataProcessor::saveToDatabase(const QString& sensorId, const QList<QSharedPointer<DataItem>>& data)
{
    try {
        logTrace(QString("Saving %1 data items to database for sensor: %2").arg(data.size()).arg(sensorId));

        // 使用现有的UploadDataHandler进行数据库操作
        UploadDataHandler dataHandler;
        dataHandler.setSensorId(sensorId);
        dataHandler.addData(data);

        // 解析数据
        bool parseResult = dataHandler.parseData();
        if (!parseResult) {
            logError(QString("UploadDataHandler parse failed for sensor: %1").arg(sensorId));
            return false;
        }

        // 保存到数据库
        bool saveResult = dataHandler.saveDataToDB();
        if (!saveResult) {
            logError(QString("UploadDataHandler save to DB failed for sensor: %1").arg(sensorId));
            return false;
        }

        logTrace(QString("Successfully saved %1 items to database for sensor: %2").arg(data.size()).arg(sensorId));
        return true;

    } catch (const std::exception& e) {
        logError(QString("Exception in saveToDatabase for sensor %1: %2").arg(sensorId).arg(e.what()));
        return false;
    } catch (...) {
        logError(QString("Unknown exception in saveToDatabase for sensor: %1").arg(sensorId));
        return false;
    }
}

bool DataProcessor::validateDataItem(const QSharedPointer<DataItem>& item) const
{
    if (item.isNull()) {
        logTrace("Null data item encountered");
        return false;
    }

    if (item->getContent().isEmpty()) {
        logTrace(QString("Empty content in data item with ID: %1").arg(item->getDataId()));
        return false;
    }

    // 可以添加更多验证逻辑
    return true;
}

void DataProcessor::updateStatistics(int processedCount, bool success, qint64 processingTime)
{
    try {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);

        m_totalProcessed += processedCount;

        if (success) {
            m_totalSuccessful += processedCount;
        } else {
            m_totalFailed += processedCount;
        }

        m_totalProcessingTime += processingTime;

        // 更新最大和最小处理时间
        qint64 currentMax = m_maxProcessingTime.load();
        if (processingTime > currentMax) {
            m_maxProcessingTime.store(processingTime);
        }

        qint64 currentMin = m_minProcessingTime.load();
        if (processingTime < currentMin) {
            m_minProcessingTime.store(processingTime);
        }

    } catch (const std::exception& e) {
        logError(QString("Exception in updateStatistics: %1").arg(e.what()));
    } catch (...) {
        logError("Unknown exception in updateStatistics");
    }
}

QString DataProcessor::getProcessingStatistics() const
{
    std::lock_guard<std::mutex> lock(m_statisticsMutex);

    size_t total = m_totalProcessed.load();
    size_t successful = m_totalSuccessful.load();
    size_t failed = m_totalFailed.load();
    qint64 totalTime = m_totalProcessingTime.load();
    qint64 maxTime = m_maxProcessingTime.load();
    qint64 minTime = m_minProcessingTime.load();

    double successRate = (total > 0) ? (static_cast<double>(successful) / total * 100.0) : 0.0;
    double avgTime = (total > 0) ? (static_cast<double>(totalTime) / total) : 0.0;

    return QString("Processing Statistics - Total: %1, Successful: %2, Failed: %3, Success Rate: %4%, "
                  "Avg Time: %5ms, Min Time: %6ms, Max Time: %7ms")
           .arg(total).arg(successful).arg(failed).arg(successRate, 0, 'f', 2)
           .arg(avgTime, 0, 'f', 2).arg(minTime == LLONG_MAX ? 0 : minTime).arg(maxTime);
}

void DataProcessor::resetStatistics()
{
    std::lock_guard<std::mutex> lock(m_statisticsMutex);

    m_totalProcessed.store(0);
    m_totalSuccessful.store(0);
    m_totalFailed.store(0);
    m_totalProcessingTime.store(0);
    m_maxProcessingTime.store(0);
    m_minProcessingTime.store(LLONG_MAX);

    logInfo("Processing statistics reset");
}
