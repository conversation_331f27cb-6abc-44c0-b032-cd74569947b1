#ifndef CACHEMANAGER_H
#define CACHEMANAGER_H

#include <QObject>
#include <QString>
#include <QStringList>
#include <QHash>
#include <QSharedPointer>
#include <QReadWriteLock>
#include <memory>
#include <atomic>
#include "sensordatacache.h"

/**
 * @brief 缓存管理器类
 * 
 * 负责管理所有传感器的数据缓存，提供统一的数据添加接口和缓存大小控制。
 * 支持动态创建传感器缓存，并提供线程安全的访问操作。
 */
class CacheManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit CacheManager(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~CacheManager();

    /**
     * @brief 设置最大总缓存大小
     * @param maxSize 最大缓存大小（数据项数量）
     */
    void setMaxTotalSize(size_t maxSize);

    /**
     * @brief 获取最大总缓存大小
     * @return 最大缓存大小
     */
    size_t getMaxTotalSize() const { return m_maxTotalSize; }

    /**
     * @brief 添加数据到指定传感器缓存
     * @param sensorId 传感器ID
     * @param items 要添加的数据项列表
     * @return true表示添加成功，false表示缓存已满
     */
    bool addData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& items);

    /**
     * @brief 获取指定传感器的缓存对象
     * @param sensorId 传感器ID
     * @return 传感器缓存对象指针，如果不存在则返回nullptr
     * @note 返回的指针在调用期间有效，但不保证长期有效性
     */
    SensorDataCache* getSensorCache(const QString& sensorId);

    /**
     * @brief 获取所有传感器ID列表
     * @return 传感器ID字符串列表
     */
    QStringList getAllSensorIds() const;

    /**
     * @brief 获取当前总缓存大小
     * @return 当前缓存中的数据项总数
     */
    size_t getTotalSize() const { return m_totalSize.load(); }

    /**
     * @brief 检查添加指定大小的数据是否会超过限制
     * @param additionalSize 要添加的数据项数量
     * @return true表示会超过限制
     */
    bool isOverLimit(size_t additionalSize) const;

    /**
     * @brief 获取缓存统计信息
     * @return 包含各传感器缓存大小的哈希表
     */
    QHash<QString, size_t> getCacheStatistics() const;

    /**
     * @brief 清空指定传感器的缓存
     * @param sensorId 传感器ID
     * @return 清空的数据项数量
     */
    size_t clearSensorCache(const QString& sensorId);

    /**
     * @brief 清空所有传感器缓存
     * @return 清空的数据项总数
     */
    size_t clearAllCaches();

signals:
    /**
     * @brief 缓存大小变化信号
     * @param totalSize 当前总缓存大小
     * @param maxSize 最大缓存大小
     */
    void cacheSizeChanged(size_t totalSize, size_t maxSize);

private:
    /**
     * @brief 获取或创建传感器缓存
     * @param sensorId 传感器ID
     * @return 传感器缓存对象指针
     */
    SensorDataCache* getOrCreateSensorCache(const QString& sensorId);

    /**
     * @brief 更新总缓存大小
     * @param delta 变化量（可为负数）
     */
    void updateTotalSize(int delta);

    /**
     * @brief 重新计算总缓存大小
     * @note 用于修正可能的计数错误
     */
    void recalculateTotalSize();

private:
    QHash<QString, QSharedPointer<SensorDataCache>> m_caches;  ///< 传感器缓存映射表
    mutable QReadWriteLock m_cachesLock;                       ///< 缓存映射表读写锁
    std::atomic<size_t> m_totalSize{0};                        ///< 当前总缓存大小
    size_t m_maxTotalSize{10000};                              ///< 最大总缓存大小

    // 禁用拷贝构造和赋值操作
    CacheManager(const CacheManager&) = delete;
    CacheManager& operator=(const CacheManager&) = delete;
};

#endif // CACHEMANAGER_H
