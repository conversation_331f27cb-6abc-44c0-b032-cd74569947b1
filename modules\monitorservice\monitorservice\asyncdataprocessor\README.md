# AsyncDataProcessor 异步数据处理器

## 概述

AsyncDataProcessor是一个高性能的异步数据处理器，专为ARM单核Linux环境下的传感器数据处理而设计。它提供了公平调度、背压控制、批处理等核心功能。

## 主要特性

- **公平调度**: 确保每个传感器都有平等的处理机会
- **背压控制**: 防止内存溢出，支持分级背压机制
- **批处理优化**: 可配置的批处理大小，支持空闲时动态调整
- **线程安全**: 全面的线程安全设计，适合多线程环境
- **错误处理**: 完善的异常处理和错误恢复机制
- **性能监控**: 详细的统计信息和性能指标

## 架构组件

### 核心组件

1. **SensorDataCache**: 单个传感器的数据缓存
2. **CacheManager**: 缓存管理器，管理所有传感器缓存
3. **ProcessScheduler**: 处理调度器，实现公平轮询调度
4. **DataProcessor**: 数据处理器，负责实际的数据处理
5. **BackpressureController**: 背压控制器，监控系统负载
6. **AsyncDataProcessor**: 主控制器，协调各个组件

## 使用示例

### 基本使用

```cpp
#include "asyncdataprocessor.h"

// 创建数据处理器
AsyncDataProcessor processor;

// 配置参数
AsyncDataProcessor::Config config;
config.maxTotalCacheSize = 10000;  // 最大缓存10000条数据
config.batchSize = 25;             // 基础批处理大小
config.maxBatchSize = 100;         // 最大批处理大小
config.timeThreshold = 3000;       // 3秒时间阈值
config.idleThreshold = 5000;       // 5秒空闲阈值
config.processingThreads = 2;      // 2个处理线程

// 设置背压回调
processor.setBackpressureCallback([](const QString& sensorId, bool isBlocked) {
    if (isBlocked) {
        qDebug() << "Sensor" << sensorId << "is blocked due to backpressure";
    } else {
        qDebug() << "Sensor" << sensorId << "backpressure warning";
    }
});

// 启动处理器
if (processor.start(config)) {
    qDebug() << "AsyncDataProcessor started successfully";
} else {
    qDebug() << "Failed to start AsyncDataProcessor";
    return;
}

// 添加数据
QList<QSharedPointer<DataItem>> dataItems;
// ... 填充数据项 ...

bool success = processor.addData("sensor001", dataItems);
if (!success) {
    qDebug() << "Failed to add data, possibly due to backpressure";
}

// 获取统计信息
QString stats = processor.getPerformanceStatistics();
qDebug() << stats;

// 停止处理器
processor.stop();
```

### 信号连接

```cpp
// 连接信号
connect(&processor, &AsyncDataProcessor::dataProcessed,
        [](const QString& sensorId, int count) {
            qDebug() << "Processed" << count << "items for sensor" << sensorId;
        });

connect(&processor, &AsyncDataProcessor::processingError,
        [](const QString& sensorId, const QString& error) {
            qDebug() << "Processing error for sensor" << sensorId << ":" << error;
        });

connect(&processor, &AsyncDataProcessor::backpressureTriggered,
        [](const QString& sensorId, bool isBlocked) {
            qDebug() << "Backpressure triggered for sensor" << sensorId 
                     << (isBlocked ? "(blocked)" : "(warning)");
        });
```

## 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| maxTotalCacheSize | 10000 | 总缓存大小限制（数据项数量） |
| batchSize | 25 | 基础批处理大小 |
| maxBatchSize | 100 | 最大批处理大小（空闲时使用） |
| timeThreshold | 3000 | 时间阈值（毫秒），超过此时间强制处理 |
| idleThreshold | 5000 | 空闲时间阈值（毫秒），超过此时间使用最大批处理 |
| processingThreads | 2 | 处理线程数（建议ARM单核环境使用2个） |
| schedulingInterval | 100 | 调度间隔（毫秒） |
| warningThreshold | 0.8 | 背压警告阈值（80%缓存使用率） |
| throttleThreshold | 0.9 | 背压限流阈值（90%缓存使用率） |
| blockThreshold | 0.95 | 背压阻塞阈值（95%缓存使用率） |

## 性能调优建议

### ARM单核环境优化

1. **线程数量**: 建议使用2个处理线程，平衡I/O等待和CPU使用
2. **批处理大小**: 根据数据库性能调整，建议25-50之间
3. **调度间隔**: 100ms通常是合适的，可根据实时性要求调整

### 内存优化

1. **缓存大小**: 根据可用内存设置，避免过大导致内存不足
2. **背压阈值**: 及时触发背压，防止内存溢出
3. **数据项大小**: 注意DataItem的内容大小，避免单个数据项过大

### 错误处理

1. **监控日志**: 关注ERROR和WARNING级别的日志
2. **统计信息**: 定期检查性能统计，发现异常
3. **背压信号**: 及时响应背压信号，调整数据产生速率

## 注意事项

1. **线程安全**: 所有公共接口都是线程安全的
2. **资源清理**: 确保在程序退出前调用stop()方法
3. **异常处理**: 组件内部已处理大部分异常，但仍需关注日志
4. **性能监控**: 建议定期检查统计信息，优化配置参数

## 编译要求

- Qt 5.x 或更高版本
- C++11 支持
- 支持 std::thread 和 std::atomic
- 依赖现有的 log 模块和 DataItem 类

## 集成说明

1. 将 asyncdataprocessor.pri 包含到主项目文件中
2. 确保依赖的头文件路径正确
3. 链接必要的Qt模块（core, concurrent）
4. 在ARM环境下启用相应的编译优化
