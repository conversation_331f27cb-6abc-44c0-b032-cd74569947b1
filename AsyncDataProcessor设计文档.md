# AsyncDataProcessor 异步数据处理器设计文档

## 1. 设计概述

### 1.1 设计目标
基于传感器上送数据结构 `QList<QSharedPointer<DataItem>>` 实现高性能异步数据处理，满足ARM单核Linux环境下的性能要求。

### 1.2 核心架构
```
AsyncDataProcessor
├── CacheManager        (缓存管理)
├── ProcessScheduler    (处理调度) 
├── DataProcessor       (数据处理)
└── BackpressureController (背压控制)
```

## 2. 需求实现对比

| 需求 | 实现方案 | 状态 |
|------|----------|------|
| 每个传感器独立缓存数据 | SensorDataCache为每个传感器维护独立队列 | ✅ 已实现 |
| 控制总缓存大小避免内存溢出 | CacheManager监控总缓存大小，超限拒绝 | ✅ 已实现 |
| 背压机制通知外部消费者 | BackpressureController回调通知 | ✅ 已实现 |
| 每个传感器缓存数据独立处理 | ProcessScheduler轮询调度确保公平性 | ✅ 已实现 |
| 不能长时间只处理1个传感器 | 轮询队列+时间阈值防止饿死 | ✅ 已实现 |
| 减少内存拷贝开销 | 使用QSharedPointer+移动语义 | ✅ 已实现 |
| 配置单次批量处理数据量大小 | Config::batchSize可配置 | ✅ 已实现 |
| 空闲时增大批处理数量 | 基于全局空闲时间动态调整批处理大小 | ✅ 已实现 |
| 处理架构逻辑清晰明了 | 组件职责单一，接口简洁 | ✅ 已实现 |
| ARM单核环境线程优化 | 1调度线程+2处理线程，减少上下文切换 | ✅ 已实现 |

## 3. 数据类型选择（基于性能考量）

### 3.1 容器类型选择
- **队列容器**: `std::queue<QSharedPointer<DataItem>>` 
  - 理由：std::queue在单线程环境下性能优于QQueue，且支持移动语义
- **哈希表**: `QHash<QString, std::unique_ptr<SensorDataCache>>`
  - 理由：QString作为key时QHash性能优于std::unordered_map
- **数组容器**: `QList<QSharedPointer<DataItem>>`（保持接口兼容）
  - 理由：与现有代码接口保持一致，避免转换开销

### 3.2 线程同步类型
- **互斥锁**: `std::mutex` / `QReadWriteLock`
  - 理由：QReadWriteLock提供更好的跨平台兼容性，支持读写分离
- **原子类型**: `std::atomic<size_t>` / `std::atomic<qint64>`
  - 理由：标准库原子操作性能更优

### 3.3 智能指针
- **数据项**: `QSharedPointer<DataItem>`（保持现有）
- **组件管理**: `std::unique_ptr`
  - 理由：std::unique_ptr开销更小，适合组件管理

## 4. 核心组件设计

### 4.1 SensorDataCache - 单传感器缓存
```cpp
class SensorDataCache {
private:
    QString m_sensorId;
    std::queue<QSharedPointer<DataItem>> m_dataQueue;
    std::atomic<size_t> m_size{0};
    std::atomic<qint64> m_lastAddTime{0};
    mutable std::mutex m_mutex;

public:
    void addData(const QList<QSharedPointer<DataItem>>& items);
    QList<QSharedPointer<DataItem>> extractData(size_t maxCount);
    
    size_t size() const { return m_size.load(); }
    qint64 lastAddTime() const { return m_lastAddTime.load(); }
    bool isEmpty() const { return m_size.load() == 0; }
};
```

### 4.2 CacheManager - 缓存管理器
```cpp
class CacheManager {
private:
    QHash<QString, std::unique_ptr<SensorDataCache>> m_caches;
    std::atomic<size_t> m_totalSize{0};
    size_t m_maxTotalSize;
    mutable std::shared_mutex m_cachesLock;

public:
    bool addData(const QString& sensorId, 
                 const QList<QSharedPointer<DataItem>>& items);
    SensorDataCache* getSensorCache(const QString& sensorId);
    QStringList getAllSensorIds() const;
    
    size_t getTotalSize() const { return m_totalSize.load(); }
    bool isOverLimit(size_t additionalSize) const;
};
```

### 4.3 ProcessScheduler - 处理调度器
```cpp
class ProcessScheduler {
private:
    QString m_lastProcessedSensorId;      // 上次处理的传感器ID
    QSet<QString> m_processingSensors;    // 正在处理的传感器集合
    std::mutex m_processingMutex;         // 处理状态保护锁
    size_t m_batchSize;                   // 批处理大小
    qint64 m_timeThreshold;               // 时间阈值
    qint64 m_idleThreshold;               // 空闲时间阈值

public:
    // 获取下一个需要处理的传感器（公平轮询）
    QString getNextSensorToProcess(CacheManager* cacheManager);

    // 标记传感器处理完成
    void markProcessingComplete(const QString& sensorId);

    // 计算批处理大小（考虑空闲时间）
    size_t calculateBatchSize(qint64 timeSinceLastGlobalUpdate) const;

    // 检查传感器是否需要处理
    bool shouldProcess(const SensorDataCache& cache) const;

private:
    // 找到上次处理传感器的下一个位置
    int findStartIndex(const QStringList& sensorIds) const;
};
```

### 4.4 DataProcessor - 数据处理器
```cpp
class DataProcessor {
public:
    bool processData(const QString& sensorId, 
                    const QList<QSharedPointer<DataItem>>& data);
    
private:
    bool parseData(const QList<QSharedPointer<DataItem>>& data);
    bool saveToDatabase(const QList<QSharedPointer<DataItem>>& data);
};
```

### 4.5 BackpressureController - 背压控制器
```cpp
class BackpressureController {
private:
    std::function<void(const QString&, bool)> m_backpressureCallback;
    double m_warningThreshold = 0.8;
    double m_blockThreshold = 0.95;

public:
    void setBackpressureCallback(std::function<void(const QString&, bool)> callback);
    void checkAndTriggerBackpressure(const QString& sensorId, 
                                   size_t currentSize, size_t maxSize);
};
```

## 5. 主控制器设计

### 5.1 配置参数
```cpp
struct Config {
    size_t maxTotalCacheSize = 10000;    // 总缓存大小限制
    size_t batchSize = 25;               // 基础批处理大小
    size_t maxBatchSize = 100;           // 最大批处理大小
    qint64 timeThreshold = 3000;         // 处理时间阈值(ms)
    qint64 idleThreshold = 5000;         // 空闲时间阈值(ms)
    size_t processingThreads = 2;        // 处理线程数
};
```

### 5.2 主要接口
```cpp
class AsyncDataProcessor : public QObject {
    Q_OBJECT
    
public:
    void start(const Config& config);
    void stop();
    
    // 主要数据接口
    bool addData(const QString& sensorId, 
                 const QList<QSharedPointer<DataItem>>& items);
    
    // 背压回调设置
    void setBackpressureCallback(std::function<void(const QString&, bool)> callback);

signals:
    void backpressureTriggered(const QString& sensorId, bool isBlocked);
    void dataProcessed(const QString& sensorId, int count);
};
```

## 6. 性能优化策略

### 6.1 内存优化
- 使用智能指针避免深拷贝
- 批处理时使用移动语义
- 处理完成后立即释放引用
- 使用std::queue减少内存重分配

### 6.2 线程优化
- ARM单核环境：1调度线程+2处理线程
- 调度线程负责轻量级决策
- 处理线程负责I/O密集型操作
- 使用原子操作减少锁竞争

### 6.3 调度优化
- 轮询调度确保公平性
- 时间阈值防止数据积压
- 空闲时增大批处理提高吞吐量

## 7. 调度算法详细设计

### 7.1 公平轮询调度算法
```cpp
QString ProcessScheduler::getNextSensorToProcess(CacheManager* cacheManager) {
    QStringList sensorIds = cacheManager->getAllSensorIds();
    if (sensorIds.isEmpty()) {
        return QString();
    }

    // 找到上次处理的传感器在当前列表中的位置
    int startIndex = 0;
    if (!m_lastProcessedSensorId.isEmpty()) {
        int lastIndex = sensorIds.indexOf(m_lastProcessedSensorId);
        if (lastIndex >= 0) {
            // 从上次处理的传感器的下一个开始
            startIndex = (lastIndex + 1) % sensorIds.size();
        }
        // 如果上次的传感器已经不在列表中，从头开始
    }

    std::lock_guard<std::mutex> lock(m_processingMutex);

    // 从startIndex开始循环查找需要处理且未在处理中的传感器
    for (int i = 0; i < sensorIds.size(); ++i) {
        int currentIndex = (startIndex + i) % sensorIds.size();
        QString sensorId = sensorIds[currentIndex];

        // 跳过正在处理的传感器
        if (m_processingSensors.contains(sensorId)) {
            continue;
        }

        SensorDataCache* cache = cacheManager->getSensorCache(sensorId);
        if (cache && shouldProcess(*cache)) {
            m_processingSensors.insert(sensorId);  // 标记为处理中
            m_lastProcessedSensorId = sensorId;    // 更新记录
            return sensorId;
        }
    }

    return QString(); // 没有传感器需要处理
}

void ProcessScheduler::markProcessingComplete(const QString& sensorId) {
    std::lock_guard<std::mutex> lock(m_processingMutex);
    m_processingSensors.remove(sensorId);
    // 不做其他操作，让调度器在下次周期中自然发现新数据
}
```

### 7.2 调度算法特点
1. **公平轮询**：基于传感器ID轮询，确保每个传感器都有处理机会
2. **动态适应**：自动处理传感器的加入和离开
3. **避免重复**：正在处理的传感器不会被重复提交
4. **状态简单**：只记录上次处理的传感器ID和正在处理的传感器集合

## 8. 时序图

### 8.1 数据添加时序图
```mermaid
sequenceDiagram
    participant Client as 外部调用者
    participant ADP as AsyncDataProcessor
    participant CM as CacheManager
    participant SC as SensorDataCache
    participant BC as BackpressureController

    Client->>ADP: addData(sensorId, items)
    ADP->>CM: isOverLimit(items.size())
    alt 缓存未满
        CM->>ADP: false
        ADP->>CM: addData(sensorId, items)
        CM->>SC: addData(items)
        SC->>SC: 更新队列和计数器
        CM->>CM: 更新总缓存大小
        ADP->>ADP: 更新全局最后更新时间
        ADP->>Client: true (成功)
    else 缓存已满
        CM->>ADP: true
        ADP->>BC: checkAndTriggerBackpressure()
        BC->>Client: 背压回调通知
        ADP->>Client: false (失败)
    end
```

### 8.2 数据处理调度时序图
```mermaid
sequenceDiagram
    participant ST as 调度线程
    participant PS as ProcessScheduler
    participant CM as CacheManager
    participant SC as SensorDataCache
    participant PT as 处理线程
    participant DP as DataProcessor

    loop 每100ms调度周期
        ST->>PS: getNextSensorToProcess()
        PS->>CM: getAllSensorIds()
        CM->>PS: sensorIds
        PS->>PS: 从上次处理位置开始轮询
        PS->>PS: 检查传感器是否正在处理中
        alt 找到需要处理的传感器
            PS->>PS: 标记传感器为处理中
            PS->>ST: sensorId
            ST->>PS: calculateBatchSize()
            PS->>ST: batchSize
            ST->>SC: extractData(batchSize)
            SC->>ST: dataItems
            ST->>PT: 提交处理任务
            PT->>DP: processData(sensorId, items)
            DP->>DP: parseData()
            DP->>DP: saveToDatabase()
            DP->>PS: markProcessingComplete(sensorId)
            PS->>PS: 移除处理中标记
        else 无传感器需要处理
            PS->>ST: 空字符串
            ST->>ST: sleep(100ms)
        end
    end
```

## 9. 流程图

### 9.1 数据添加流程图
```mermaid
flowchart TD
    A[外部调用addData] --> B{检查总缓存是否超限}
    B -->|超限| C[触发背压机制]
    C --> D[返回false]
    B -->|未超限| E[获取传感器缓存]
    E --> F{传感器缓存是否存在}
    F -->|不存在| G[创建新的传感器缓存]
    G --> H[添加数据到缓存]
    F -->|存在| H
    H --> I[更新缓存计数器]
    I --> J[更新全局最后更新时间]
    J --> K[返回true]
```

### 9.2 公平调度流程图
```mermaid
flowchart TD
    A[调度线程启动] --> B[获取所有传感器ID列表]
    B --> C[找到上次处理传感器的下一个位置]
    C --> D[从该位置开始轮询]
    D --> E{当前传感器是否正在处理中}
    E -->|是| F[跳过,检查下一个传感器]
    F --> G{是否已检查完所有传感器}
    G -->|否| D
    G -->|是| H[无传感器需要处理,等待下次调度]
    E -->|否| I{传感器是否需要处理}
    I -->|否| F
    I -->|是| J[标记传感器为处理中]
    J --> K[计算批处理大小]
    K --> L[从缓存提取数据]
    L --> M[提交给处理线程]
    M --> N[记录为上次处理的传感器]
    N --> O[等待下次调度周期]
    O --> B
```

### 9.3 批处理大小计算流程图
```mermaid
flowchart TD
    A[计算批处理大小] --> B[获取全局最后更新时间]
    B --> C[计算距离当前时间间隔]
    C --> D{间隔是否超过空闲阈值}
    D -->|是| E[使用最大批处理大小]
    D -->|否| F[使用基础批处理大小]
    E --> G[返回批处理大小]
    F --> G
```

### 9.4 处理任务完成流程图
```mermaid
flowchart TD
    A[处理任务开始] --> B[从缓存提取数据]
    B --> C[解析数据]
    C --> D[保存到数据库]
    D --> E[标记传感器处理完成]
    E --> F[任务结束]
    F --> G[等待调度器下次检查]

    Note1[不立即检查新数据<br/>避免传感器独占]
    E -.-> Note1
```

## 10. 关键设计决策

### 10.1 调度策略
- **公平轮询**：基于传感器ID轮询，确保每个传感器都有处理机会
- **状态跟踪**：记录正在处理的传感器，避免重复提交任务
- **动态适应**：自动处理传感器的加入和离开
- **简单高效**：不立即检查新数据，让调度器自然轮询

### 10.2 线程模型
- **调度线程**：1个，负责轻量级调度决策，每100ms运行一次
- **处理线程**：2个，负责数据解析和数据库I/O
- **主线程**：负责数据接收，使用原子操作保证线程安全

### 10.3 内存管理
- 使用`QSharedPointer<DataItem>`避免数据拷贝
- 批处理时通过移动语义传递数据
- 处理完成后立即释放智能指针引用

### 10.4 并发控制
- **避免任务堆积**：同一传感器不会被重复提交处理任务
- **防止独占**：任务完成后不立即检查新数据，保证公平性
- **状态一致**：使用互斥锁保护处理状态，确保线程安全

### 10.5 背压控制
- 80%缓存使用率触发警告
- 95%缓存使用率触发阻塞
- 通过回调函数通知外部系统

## 10. 配置建议

### 10.1 默认配置
```cpp
Config defaultConfig = {
    .maxTotalCacheSize = 10000,     // 总缓存10000条
    .batchSize = 25,                // 基础批处理25条
    .maxBatchSize = 100,            // 最大批处理100条
    .timeThreshold = 3000,          // 3秒时间阈值
    .idleThreshold = 5000,          // 5秒空闲阈值
    .processingThreads = 2          // 2个处理线程
};
```

### 10.2 性能调优建议
- **高吞吐场景**：增大batchSize和maxBatchSize
- **低延迟场景**：减小timeThreshold
- **内存受限场景**：减小maxTotalCacheSize
- **CPU受限场景**：减少processingThreads到1个
