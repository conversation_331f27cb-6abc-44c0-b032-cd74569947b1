#ifndef PROCESSSCHEDULER_H
#define PROCESSSCHEDULER_H

#include <QString>
#include <QStringList>
#include <QSet>
#include <mutex>
#include <atomic>
#include "cachemanager.h"

/**
 * @brief 处理调度器类
 * 
 * 负责公平调度各传感器的数据处理，确保每个传感器都有机会被处理。
 * 使用轮询算法和状态跟踪机制，避免重复提交任务和传感器饿死问题。
 */
class ProcessScheduler
{
public:
    /**
     * @brief 构造函数
     */
    ProcessScheduler();

    /**
     * @brief 析构函数
     */
    ~ProcessScheduler();

    /**
     * @brief 设置批处理大小
     * @param batchSize 批处理大小
     */
    void setBatchSize(size_t batchSize);

    /**
     * @brief 设置时间阈值
     * @param timeThreshold 时间阈值（毫秒）
     */
    void setTimeThreshold(qint64 timeThreshold);

    /**
     * @brief 设置空闲时间阈值
     * @param idleThreshold 空闲时间阈值（毫秒）
     */
    void setIdleThreshold(qint64 idleThreshold);

    /**
     * @brief 设置最大批处理大小
     * @param maxBatchSize 最大批处理大小
     */
    void setMaxBatchSize(size_t maxBatchSize);

    /**
     * @brief 获取下一个需要处理的传感器（公平轮询）
     * @param cacheManager 缓存管理器指针
     * @return 需要处理的传感器ID，如果没有则返回空字符串
     */
    QString getNextSensorToProcess(CacheManager* cacheManager);

    /**
     * @brief 标记传感器处理完成
     * @param sensorId 传感器ID
     */
    void markProcessingComplete(const QString& sensorId);

    /**
     * @brief 计算批处理大小（考虑空闲时间）
     * @param timeSinceLastGlobalUpdate 距离上次全局更新的时间间隔（毫秒）
     * @return 计算得出的批处理大小
     */
    size_t calculateBatchSize(qint64 timeSinceLastGlobalUpdate) const;

    /**
     * @brief 检查传感器是否需要处理
     * @param cache 传感器缓存对象
     * @return true表示需要处理
     */
    bool shouldProcess(const SensorDataCache& cache) const;

    /**
     * @brief 获取当前正在处理的传感器数量
     * @return 正在处理的传感器数量
     */
    size_t getProcessingCount() const;

    /**
     * @brief 获取调度统计信息
     * @return 包含统计信息的字符串
     */
    QString getSchedulingStatistics() const;

    /**
     * @brief 重置调度状态
     * @note 清空所有处理状态，用于重启或错误恢复
     */
    void resetSchedulingState();

private:
    /**
     * @brief 找到上次处理传感器的下一个位置
     * @param sensorIds 传感器ID列表
     * @return 开始搜索的索引位置
     */
    int findStartIndex(const QStringList& sensorIds) const;

private:
    QString m_lastProcessedSensorId;        ///< 上次处理的传感器ID
    QSet<QString> m_processingSensors;      ///< 正在处理的传感器集合
    mutable std::mutex m_processingMutex;   ///< 处理状态保护锁

    // 配置参数
    size_t m_batchSize{25};                 ///< 基础批处理大小
    size_t m_maxBatchSize{100};             ///< 最大批处理大小
    qint64 m_timeThreshold{3000};           ///< 时间阈值（毫秒）
    qint64 m_idleThreshold{5000};           ///< 空闲时间阈值（毫秒）

    // 统计信息
    std::atomic<size_t> m_totalScheduled{0};    ///< 总调度次数
    std::atomic<size_t> m_totalProcessed{0};    ///< 总处理次数

    // 禁用拷贝构造和赋值操作
    ProcessScheduler(const ProcessScheduler&) = delete;
    ProcessScheduler& operator=(const ProcessScheduler&) = delete;
};

#endif // PROCESSSCHEDULER_H
