#ifndef BACKPRESSURECONTROLLER_H
#define BACKPRESSURECONTROLLER_H

#include <QObject>
#include <QString>
#include <functional>
#include <atomic>
#include <mutex>

/**
 * @brief 背压控制器类
 * 
 * 负责监控系统负载并触发背压机制，防止系统过载。
 * 支持分级背压控制和自定义回调通知。
 */
class BackpressureController : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 背压级别枚举
     */
    enum class BackpressureLevel {
        None = 0,       ///< 无背压
        Warning = 1,    ///< 警告级别
        Throttle = 2,   ///< 限流级别
        Block = 3       ///< 阻塞级别
    };

    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit BackpressureController(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~BackpressureController();

    /**
     * @brief 设置背压回调函数
     * @param callback 回调函数，参数为(传感器ID, 是否阻塞)
     */
    void setBackpressureCallback(std::function<void(const QString&, bool)> callback);

    /**
     * @brief 设置警告阈值
     * @param threshold 警告阈值（0.0-1.0）
     */
    void setWarningThreshold(double threshold);

    /**
     * @brief 设置限流阈值
     * @param threshold 限流阈值（0.0-1.0）
     */
    void setThrottleThreshold(double threshold);

    /**
     * @brief 设置阻塞阈值
     * @param threshold 阻塞阈值（0.0-1.0）
     */
    void setBlockThreshold(double threshold);

    /**
     * @brief 检查并触发背压机制
     * @param sensorId 传感器ID
     * @param currentSize 当前缓存大小
     * @param maxSize 最大缓存大小
     * @return 当前背压级别
     */
    BackpressureLevel checkAndTriggerBackpressure(const QString& sensorId, size_t currentSize, size_t maxSize);

    /**
     * @brief 获取当前背压级别
     * @return 当前背压级别
     */
    BackpressureLevel getCurrentLevel() const { return m_currentLevel.load(); }

    /**
     * @brief 获取背压统计信息
     * @return 包含统计信息的字符串
     */
    QString getBackpressureStatistics() const;

    /**
     * @brief 重置背压统计信息
     */
    void resetStatistics();

    /**
     * @brief 手动触发背压
     * @param sensorId 传感器ID
     * @param level 背压级别
     */
    void triggerBackpressure(const QString& sensorId, BackpressureLevel level);

signals:
    /**
     * @brief 背压级别变化信号
     * @param sensorId 传感器ID
     * @param level 新的背压级别
     * @param utilizationRate 当前利用率
     */
    void backpressureLevelChanged(const QString& sensorId, int level, double utilizationRate);

    /**
     * @brief 背压警告信号
     * @param sensorId 传感器ID
     * @param message 警告信息
     */
    void backpressureWarning(const QString& sensorId, const QString& message);

private:
    /**
     * @brief 计算利用率
     * @param currentSize 当前大小
     * @param maxSize 最大大小
     * @return 利用率（0.0-1.0）
     */
    double calculateUtilizationRate(size_t currentSize, size_t maxSize) const;

    /**
     * @brief 确定背压级别
     * @param utilizationRate 利用率
     * @return 背压级别
     */
    BackpressureLevel determineBackpressureLevel(double utilizationRate) const;

    /**
     * @brief 背压级别转字符串
     * @param level 背压级别
     * @return 级别字符串
     */
    QString backpressureLevelToString(BackpressureLevel level) const;

    /**
     * @brief 更新统计信息
     * @param level 触发的背压级别
     */
    void updateStatistics(BackpressureLevel level);

private:
    std::function<void(const QString&, bool)> m_backpressureCallback;  ///< 背压回调函数
    
    // 阈值配置
    double m_warningThreshold{0.8};     ///< 警告阈值
    double m_throttleThreshold{0.9};    ///< 限流阈值
    double m_blockThreshold{0.95};      ///< 阻塞阈值
    
    // 状态信息
    std::atomic<BackpressureLevel> m_currentLevel{BackpressureLevel::None};  ///< 当前背压级别
    
    // 统计信息
    std::atomic<size_t> m_warningCount{0};      ///< 警告次数
    std::atomic<size_t> m_throttleCount{0};     ///< 限流次数
    std::atomic<size_t> m_blockCount{0};        ///< 阻塞次数
    std::atomic<size_t> m_totalChecks{0};       ///< 总检查次数
    
    mutable std::mutex m_statisticsMutex;       ///< 统计信息保护锁

    // 禁用拷贝构造和赋值操作
    BackpressureController(const BackpressureController&) = delete;
    BackpressureController& operator=(const BackpressureController&) = delete;
};

#endif // BACKPRESSURECONTROLLER_H
