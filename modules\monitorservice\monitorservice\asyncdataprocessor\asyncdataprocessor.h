#ifndef ASYNCDATAPROCESSOR_H
#define ASYNCDATAPROCESSOR_H

#include <QObject>
#include <QString>
#include <QSharedPointer>
#include <QList>
#include <QThreadPool>
#include <QTimer>
#include <QElapsedTimer>
#include <memory>
#include <thread>
#include <atomic>
#include <functional>
#include "cachemanager.h"
#include "processscheduler.h"
#include "dataprocessor.h"
#include "backpressurecontroller.h"
#include "../fastsyncdata/businessprocess/dataitem.h"

/**
 * @brief 异步数据处理器主控制器类
 * 
 * 负责协调各个组件，提供统一的数据处理接口。
 * 实现公平调度、背压控制、批处理等核心功能。
 */
class AsyncDataProcessor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 配置参数结构
     */
    struct Config {
        size_t maxTotalCacheSize = 10000;      ///< 总缓存大小限制
        size_t batchSize = 25;                 ///< 基础批处理大小
        size_t maxBatchSize = 100;             ///< 最大批处理大小
        qint64 timeThreshold = 3000;           ///< 时间阈值（毫秒）
        qint64 idleThreshold = 5000;           ///< 空闲时间阈值（毫秒）
        size_t processingThreads = 2;          ///< 处理线程数
        qint64 schedulingInterval = 100;       ///< 调度间隔（毫秒）
        
        // 背压控制阈值
        double warningThreshold = 0.8;         ///< 警告阈值
        double throttleThreshold = 0.9;        ///< 限流阈值
        double blockThreshold = 0.95;          ///< 阻塞阈值
    };

    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit AsyncDataProcessor(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~AsyncDataProcessor();

    /**
     * @brief 启动数据处理器
     * @param config 配置参数
     * @return true表示启动成功
     */
    bool start(const Config& config);

    /**
     * @brief 停止数据处理器
     */
    void stop();

    /**
     * @brief 检查是否正在运行
     * @return true表示正在运行
     */
    bool isRunning() const { return m_running.load(); }

    /**
     * @brief 添加数据（主要接口）
     * @param sensorId 传感器ID
     * @param items 要添加的数据项列表
     * @return true表示添加成功，false表示缓存满或其他错误
     */
    bool addData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& items);

    /**
     * @brief 设置背压回调函数
     * @param callback 回调函数，参数为(传感器ID, 是否阻塞)
     */
    void setBackpressureCallback(std::function<void(const QString&, bool)> callback);

    /**
     * @brief 获取当前配置
     * @return 当前配置参数
     */
    Config getCurrentConfig() const { return m_config; }

    /**
     * @brief 获取性能统计信息
     * @return 包含统计信息的字符串
     */
    QString getPerformanceStatistics() const;

    /**
     * @brief 获取缓存统计信息
     * @return 包含缓存统计信息的哈希表
     */
    QHash<QString, size_t> getCacheStatistics() const;

    /**
     * @brief 重置所有统计信息
     */
    void resetAllStatistics();

    /**
     * @brief 清空指定传感器的缓存
     * @param sensorId 传感器ID
     * @return 清空的数据项数量
     */
    size_t clearSensorCache(const QString& sensorId);

    /**
     * @brief 清空所有传感器缓存
     * @return 清空的数据项总数
     */
    size_t clearAllCaches();

signals:
    /**
     * @brief 背压触发信号
     * @param sensorId 传感器ID
     * @param isBlocked 是否被阻塞
     */
    void backpressureTriggered(const QString& sensorId, bool isBlocked);

    /**
     * @brief 数据处理完成信号
     * @param sensorId 传感器ID
     * @param count 处理的数据项数量
     */
    void dataProcessed(const QString& sensorId, int count);

    /**
     * @brief 处理错误信号
     * @param sensorId 传感器ID
     * @param errorMessage 错误信息
     */
    void processingError(const QString& sensorId, const QString& errorMessage);

    /**
     * @brief 状态变化信号
     * @param running 是否正在运行
     */
    void statusChanged(bool running);

private slots:
    /**
     * @brief 调度定时器槽函数
     */
    void onSchedulingTimer();

    /**
     * @brief 处理数据处理完成信号
     * @param sensorId 传感器ID
     * @param processedCount 处理的数据项数量
     * @param success 是否处理成功
     */
    void onDataProcessed(const QString& sensorId, int processedCount, bool success);

    /**
     * @brief 处理数据处理错误信号
     * @param sensorId 传感器ID
     * @param errorMessage 错误信息
     */
    void onProcessingError(const QString& sensorId, const QString& errorMessage);

private:
    /**
     * @brief 提交处理任务
     * @param sensorId 传感器ID
     */
    void submitProcessingTask(const QString& sensorId);

    /**
     * @brief 获取距离上次全局更新的时间间隔
     * @return 时间间隔（毫秒）
     */
    qint64 getTimeSinceLastGlobalUpdate() const;

    /**
     * @brief 更新全局最后更新时间
     */
    void updateGlobalLastUpdateTime();

    /**
     * @brief 初始化组件
     * @param config 配置参数
     * @return true表示初始化成功
     */
    bool initializeComponents(const Config& config);

    /**
     * @brief 清理资源
     */
    void cleanup();

private:
    // 核心组件
    std::unique_ptr<CacheManager> m_cacheManager;              ///< 缓存管理器
    std::unique_ptr<ProcessScheduler> m_scheduler;             ///< 处理调度器
    std::unique_ptr<DataProcessor> m_processor;                ///< 数据处理器
    std::unique_ptr<BackpressureController> m_backpressureController; ///< 背压控制器

    // 线程管理
    QThreadPool m_threadPool;                                  ///< 处理线程池
    QTimer* m_schedulingTimer;                                 ///< 调度定时器

    // 状态管理
    std::atomic<bool> m_running{false};                        ///< 运行状态
    Config m_config;                                           ///< 当前配置
    std::atomic<qint64> m_lastGlobalUpdateTime{0};            ///< 全局最后更新时间

    // 统计信息
    std::atomic<size_t> m_totalDataAdded{0};                  ///< 总添加数据量
    std::atomic<size_t> m_totalDataProcessed{0};              ///< 总处理数据量
    std::atomic<size_t> m_totalSchedulingCycles{0};           ///< 总调度周期数

    // 禁用拷贝构造和赋值操作
    AsyncDataProcessor(const AsyncDataProcessor&) = delete;
    AsyncDataProcessor& operator=(const AsyncDataProcessor&) = delete;
};

#endif // ASYNCDATAPROCESSOR_H
