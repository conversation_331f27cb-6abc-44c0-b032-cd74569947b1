#ifndef SENSORDATACACHE_H
#define SENSORDATACACHE_H

#include <QString>
#include <QSharedPointer>
#include <QList>
#include <QElapsedTimer>
#include <queue>
#include <mutex>
#include <atomic>
#include "../fastsyncdata/businessprocess/dataitem.h"

/**
 * @brief 单个传感器的数据缓存类
 * 
 * 负责管理单个传感器的数据队列，提供线程安全的数据添加和提取操作。
 * 使用std::queue提供高效的FIFO操作，使用原子变量提供无锁的状态查询。
 */
class SensorDataCache
{
public:
    /**
     * @brief 构造函数
     * @param sensorId 传感器ID
     */
    explicit SensorDataCache(const QString& sensorId);

    /**
     * @brief 析构函数
     */
    ~SensorDataCache();

    /**
     * @brief 添加数据项到缓存
     * @param items 要添加的数据项列表
     * @note 线程安全操作，会更新缓存大小和最后添加时间
     */
    void addData(const QList<QSharedPointer<DataItem>>& items);

    /**
     * @brief 从缓存中提取指定数量的数据项
     * @param maxCount 最大提取数量
     * @return 提取的数据项列表
     * @note 线程安全操作，实际提取数量可能小于maxCount
     */
    QList<QSharedPointer<DataItem>> extractData(size_t maxCount);

    /**
     * @brief 获取当前缓存大小
     * @return 缓存中的数据项数量
     * @note 无锁操作，性能高效
     */
    size_t size() const { return m_size.load(); }

    /**
     * @brief 获取最后添加数据的时间戳
     * @return 最后添加时间的毫秒时间戳
     */
    qint64 lastAddTime() const { return m_lastAddTime.load(); }

    /**
     * @brief 检查缓存是否为空
     * @return true表示缓存为空
     */
    bool isEmpty() const { return m_size.load() == 0; }

    /**
     * @brief 获取传感器ID
     * @return 传感器ID字符串
     */
    QString sensorId() const { return m_sensorId; }

    /**
     * @brief 计算距离最后添加数据的时间间隔
     * @return 时间间隔（毫秒）
     */
    qint64 timeSinceLastAdd() const;

private:
    QString m_sensorId;                                    ///< 传感器ID
    std::queue<QSharedPointer<DataItem>> m_dataQueue;     ///< 数据队列
    std::atomic<size_t> m_size{0};                        ///< 当前缓存大小
    std::atomic<qint64> m_lastAddTime{0};                 ///< 最后添加时间戳
    mutable std::mutex m_mutex;                           ///< 保护队列操作的互斥锁

    // 禁用拷贝构造和赋值操作
    SensorDataCache(const SensorDataCache&) = delete;
    SensorDataCache& operator=(const SensorDataCache&) = delete;
};

#endif // SENSORDATACACHE_H
