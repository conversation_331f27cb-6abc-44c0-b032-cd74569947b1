#ifndef DATAPROCESSOR_H
#define DATAPROCESSOR_H

#include <QObject>
#include <QString>
#include <QSharedPointer>
#include <QList>
#include "../fastsyncdata/businessprocess/dataitem.h"

/**
 * @brief 数据处理器类
 * 
 * 负责实际的数据处理工作，包括数据解析和数据库存储。
 * 提供批量处理接口，支持错误处理和性能统计。
 */
class DataProcessor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象指针
     */
    explicit DataProcessor(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~DataProcessor();

    /**
     * @brief 处理一批数据
     * @param sensorId 传感器ID
     * @param data 要处理的数据项列表
     * @return true表示处理成功，false表示处理失败
     */
    bool processData(const QString& sensorId, const QList<QSharedPointer<DataItem>>& data);

    /**
     * @brief 获取处理统计信息
     * @return 包含统计信息的字符串
     */
    QString getProcessingStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void resetStatistics();

signals:
    /**
     * @brief 数据处理完成信号
     * @param sensorId 传感器ID
     * @param processedCount 处理的数据项数量
     * @param success 是否处理成功
     */
    void dataProcessed(const QString& sensorId, int processedCount, bool success);

    /**
     * @brief 处理错误信号
     * @param sensorId 传感器ID
     * @param errorMessage 错误信息
     */
    void processingError(const QString& sensorId, const QString& errorMessage);

private:
    /**
     * @brief 解析数据
     * @param data 要解析的数据项列表
     * @return true表示解析成功
     */
    bool parseData(const QList<QSharedPointer<DataItem>>& data);

    /**
     * @brief 保存数据到数据库
     * @param sensorId 传感器ID
     * @param data 要保存的数据项列表
     * @return true表示保存成功
     */
    bool saveToDatabase(const QString& sensorId, const QList<QSharedPointer<DataItem>>& data);

    /**
     * @brief 验证数据项
     * @param item 要验证的数据项
     * @return true表示数据项有效
     */
    bool validateDataItem(const QSharedPointer<DataItem>& item) const;

    /**
     * @brief 更新统计信息
     * @param processedCount 处理的数据项数量
     * @param success 是否处理成功
     * @param processingTime 处理耗时（毫秒）
     */
    void updateStatistics(int processedCount, bool success, qint64 processingTime);

private:
    // 统计信息
    std::atomic<size_t> m_totalProcessed{0};        ///< 总处理数量
    std::atomic<size_t> m_totalSuccessful{0};       ///< 成功处理数量
    std::atomic<size_t> m_totalFailed{0};           ///< 失败处理数量
    std::atomic<qint64> m_totalProcessingTime{0};   ///< 总处理时间（毫秒）
    std::atomic<qint64> m_maxProcessingTime{0};     ///< 最大处理时间（毫秒）
    std::atomic<qint64> m_minProcessingTime{LLONG_MAX}; ///< 最小处理时间（毫秒）

    mutable std::mutex m_statisticsMutex;           ///< 统计信息保护锁

    // 禁用拷贝构造和赋值操作
    DataProcessor(const DataProcessor&) = delete;
    DataProcessor& operator=(const DataProcessor&) = delete;
};

#endif // DATAPROCESSOR_H
